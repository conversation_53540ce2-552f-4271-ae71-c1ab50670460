/**
 * Pattern Analysis Engine for Cosmic Music-Nature Pattern Discovery Platform
 * Implements mathematical pattern recognition algorithms for natural sounds and music
 */

import {
  NaturalSoundPattern,
  PatternSimilarity,
  MathematicalPattern,
  MathematicalPatternType,
  RhythmicPattern,
} from '@/types/NaturalPattern.types';

export interface PatternAnalysisConfig {
  fftSize: number;
  windowSize: number;
  hopSize: number;
  sampleRate: number;
  enableFractalAnalysis: boolean;
  enableFibonacciDetection: boolean;
  enableGoldenRatioAnalysis: boolean;
}

export interface PatternMatchResult {
  patternType: MathematicalPatternType;
  confidence: number;
  parameters: Record<string, number>;
  similarity: PatternSimilarity;
  visualizationData: number[];
}

export class PatternAnalyzer {
  private config: PatternAnalysisConfig;
  private fibonacciSequence: number[];
  private goldenRatio = 1.618033988749;

  constructor(config: Partial<PatternAnalysisConfig> = {}) {
    this.config = {
      fftSize: 2048,
      windowSize: 1024,
      hopSize: 512,
      sampleRate: 48000,
      enableFractalAnalysis: true,
      enableFibonacciDetection: true,
      enableGoldenRatioAnalysis: true,
      ...config,
    };

    // Pre-calculate Fibonacci sequence
    this.fibonacciSequence = this.generateFibonacciSequence(20);
  }

  /**
   * Analyze natural sound pattern for mathematical relationships
   */
  async analyzePattern(pattern: NaturalSoundPattern): Promise<PatternMatchResult[]> {
    const results: PatternMatchResult[] = [];

    try {
      // Analyze rhythmic patterns
      if (pattern.rhythmicPatterns.length > 0) {
        for (const rhythmicPattern of pattern.rhythmicPatterns) {
          const rhythmResults = await this.analyzeRhythmicPattern(rhythmicPattern);
          results.push(...rhythmResults);
        }
      }

      // Analyze frequency data
      if (pattern.frequencyData) {
        const frequencyResults = await this.analyzeFrequencyPattern(pattern.frequencyData);
        results.push(...frequencyResults);
      }

      // Analyze waveform data
      if (pattern.waveformData) {
        const waveformResults = await this.analyzeWaveformPattern(pattern.waveformData);
        results.push(...waveformResults);
      }

      return results.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      console.error('Pattern analysis failed:', error);
      throw new Error('Pattern analysis failed');
    }
  }

  /**
   * Analyze rhythmic patterns for mathematical relationships
   */
  private async analyzeRhythmicPattern(rhythmicPattern: RhythmicPattern): Promise<PatternMatchResult[]> {
    const results: PatternMatchResult[] = [];
    const intervals = rhythmicPattern.pattern;

    // Fibonacci sequence detection
    if (this.config.enableFibonacciDetection) {
      const fibonacciResult = this.detectFibonacciPattern(intervals);
      if (fibonacciResult.confidence > 0.5) {
        results.push({
          patternType: MathematicalPatternType.FIBONACCI,
          confidence: fibonacciResult.confidence,
          parameters: fibonacciResult.parameters,
          similarity: this.calculatePatternSimilarity(intervals, fibonacciResult.expectedPattern),
          visualizationData: fibonacciResult.expectedPattern,
        });
      }
    }

    // Golden ratio detection
    if (this.config.enableGoldenRatioAnalysis) {
      const goldenRatioResult = this.detectGoldenRatioPattern(intervals);
      if (goldenRatioResult.confidence > 0.5) {
        results.push({
          patternType: MathematicalPatternType.GOLDEN_RATIO,
          confidence: goldenRatioResult.confidence,
          parameters: goldenRatioResult.parameters,
          similarity: this.calculatePatternSimilarity(intervals, goldenRatioResult.expectedPattern),
          visualizationData: goldenRatioResult.expectedPattern,
        });
      }
    }

    // Geometric progression detection
    const geometricResult = this.detectGeometricProgression(intervals);
    if (geometricResult.confidence > 0.5) {
      results.push({
        patternType: MathematicalPatternType.GEOMETRIC_PROGRESSION,
        confidence: geometricResult.confidence,
        parameters: geometricResult.parameters,
        similarity: this.calculatePatternSimilarity(intervals, geometricResult.expectedPattern),
        visualizationData: geometricResult.expectedPattern,
      });
    }

    return results;
  }

  /**
   * Analyze frequency data for harmonic patterns
   */
  private async analyzeFrequencyPattern(frequencyData: any): Promise<PatternMatchResult[]> {
    const results: PatternMatchResult[] = [];

    // Extract dominant frequencies
    const dominantFreqs = this.extractDominantFrequencies(frequencyData.magnitudes);
    
    // Analyze harmonic series
    const harmonicResult = this.detectHarmonicSeries(dominantFreqs);
    if (harmonicResult.confidence > 0.6) {
      results.push({
        patternType: MathematicalPatternType.HARMONIC_SERIES,
        confidence: harmonicResult.confidence,
        parameters: harmonicResult.parameters,
        similarity: this.calculateFrequencySimilarity(dominantFreqs, harmonicResult.expectedFrequencies),
        visualizationData: harmonicResult.expectedFrequencies,
      });
    }

    return results;
  }

  /**
   * Analyze waveform data for fractal patterns
   */
  private async analyzeWaveformPattern(waveformData: Float32Array): Promise<PatternMatchResult[]> {
    const results: PatternMatchResult[] = [];

    if (this.config.enableFractalAnalysis) {
      const fractalResult = await this.calculateFractalDimension(waveformData);
      if (fractalResult.confidence > 0.5) {
        results.push({
          patternType: MathematicalPatternType.FRACTAL,
          confidence: fractalResult.confidence,
          parameters: fractalResult.parameters,
          similarity: {
            overallScore: fractalResult.confidence,
            rhythmicSimilarity: 0.5,
            melodicSimilarity: 0.5,
            harmonicSimilarity: 0.5,
            spectralSimilarity: fractalResult.confidence,
            temporalSimilarity: fractalResult.confidence,
            analysisMethod: 'fractal_dimension',
            confidence: fractalResult.confidence,
          },
          visualizationData: Array.from(waveformData.slice(0, 100)),
        });
      }
    }

    return results;
  }

  /**
   * Detect Fibonacci sequence in numerical data
   */
  private detectFibonacciPattern(data: number[]): {
    confidence: number;
    parameters: Record<string, number>;
    expectedPattern: number[];
  } {
    if (data.length < 3) {
      return { confidence: 0, parameters: {}, expectedPattern: [] };
    }

    // Normalize data to find ratios
    const ratios = [];
    for (let i = 1; i < data.length; i++) {
      if (data[i - 1] !== 0) {
        ratios.push(data[i] / data[i - 1]);
      }
    }

    // Check if ratios approach golden ratio (Fibonacci property)
    let goldenRatioMatches = 0;
    const tolerance = 0.2;

    for (const ratio of ratios) {
      if (Math.abs(ratio - this.goldenRatio) < tolerance) {
        goldenRatioMatches++;
      }
    }

    const confidence = ratios.length > 0 ? goldenRatioMatches / ratios.length : 0;

    // Generate expected Fibonacci pattern
    const expectedPattern = this.generateFibonacciLikeSequence(data[0], data.length);

    return {
      confidence,
      parameters: {
        goldenRatioMatches,
        totalRatios: ratios.length,
        averageRatio: ratios.reduce((sum, r) => sum + r, 0) / ratios.length,
      },
      expectedPattern,
    };
  }

  /**
   * Detect golden ratio proportions
   */
  private detectGoldenRatioPattern(data: number[]): {
    confidence: number;
    parameters: Record<string, number>;
    expectedPattern: number[];
  } {
    if (data.length < 2) {
      return { confidence: 0, parameters: {}, expectedPattern: [] };
    }

    let goldenRatioCount = 0;
    const tolerance = 0.15;

    // Check consecutive ratios
    for (let i = 1; i < data.length; i++) {
      if (data[i - 1] !== 0) {
        const ratio = data[i] / data[i - 1];
        if (Math.abs(ratio - this.goldenRatio) < tolerance || 
            Math.abs(ratio - (1 / this.goldenRatio)) < tolerance) {
          goldenRatioCount++;
        }
      }
    }

    const confidence = (data.length - 1) > 0 ? goldenRatioCount / (data.length - 1) : 0;

    // Generate expected golden ratio pattern
    const expectedPattern = [data[0]];
    for (let i = 1; i < data.length; i++) {
      expectedPattern.push(expectedPattern[i - 1] * this.goldenRatio);
    }

    return {
      confidence,
      parameters: {
        goldenRatioMatches: goldenRatioCount,
        totalComparisons: data.length - 1,
        goldenRatio: this.goldenRatio,
      },
      expectedPattern,
    };
  }

  /**
   * Detect geometric progression
   */
  private detectGeometricProgression(data: number[]): {
    confidence: number;
    parameters: Record<string, number>;
    expectedPattern: number[];
  } {
    if (data.length < 3) {
      return { confidence: 0, parameters: {}, expectedPattern: [] };
    }

    // Calculate ratios
    const ratios = [];
    for (let i = 1; i < data.length; i++) {
      if (data[i - 1] !== 0) {
        ratios.push(data[i] / data[i - 1]);
      }
    }

    if (ratios.length === 0) {
      return { confidence: 0, parameters: {}, expectedPattern: [] };
    }

    // Check if ratios are consistent (geometric progression)
    const averageRatio = ratios.reduce((sum, r) => sum + r, 0) / ratios.length;
    const tolerance = 0.2;
    
    let consistentRatios = 0;
    for (const ratio of ratios) {
      if (Math.abs(ratio - averageRatio) < tolerance) {
        consistentRatios++;
      }
    }

    const confidence = consistentRatios / ratios.length;

    // Generate expected geometric progression
    const expectedPattern = [data[0]];
    for (let i = 1; i < data.length; i++) {
      expectedPattern.push(expectedPattern[i - 1] * averageRatio);
    }

    return {
      confidence,
      parameters: {
        commonRatio: averageRatio,
        consistentRatios,
        totalRatios: ratios.length,
        firstTerm: data[0],
      },
      expectedPattern,
    };
  }

  /**
   * Detect harmonic series in frequency data
   */
  private detectHarmonicSeries(frequencies: number[]): {
    confidence: number;
    parameters: Record<string, number>;
    expectedFrequencies: number[];
  } {
    if (frequencies.length < 2) {
      return { confidence: 0, parameters: {}, expectedFrequencies: [] };
    }

    // Assume first frequency is fundamental
    const fundamental = frequencies[0];
    let harmonicMatches = 0;
    const tolerance = 0.05; // 5% tolerance

    const expectedFrequencies = [];
    for (let i = 0; i < frequencies.length; i++) {
      const expectedHarmonic = fundamental * (i + 1);
      expectedFrequencies.push(expectedHarmonic);

      // Check if actual frequency matches expected harmonic
      if (i < frequencies.length) {
        const actualFreq = frequencies[i];
        const relativeError = Math.abs(actualFreq - expectedHarmonic) / expectedHarmonic;
        if (relativeError < tolerance) {
          harmonicMatches++;
        }
      }
    }

    const confidence = frequencies.length > 0 ? harmonicMatches / frequencies.length : 0;

    return {
      confidence,
      parameters: {
        fundamental,
        harmonicMatches,
        totalFrequencies: frequencies.length,
        harmonicity: confidence,
      },
      expectedFrequencies,
    };
  }

  /**
   * Calculate fractal dimension using box-counting method
   */
  private async calculateFractalDimension(data: Float32Array): Promise<{
    confidence: number;
    parameters: Record<string, number>;
  }> {
    // Simplified fractal dimension calculation
    const scales = [2, 4, 8, 16, 32, 64];
    const counts = [];

    for (const scale of scales) {
      const boxCount = this.countBoxes(data, scale);
      counts.push(boxCount);
    }

    // Linear regression on log-log plot
    const logScales = scales.map(Math.log);
    const logCounts = counts.map(Math.log);
    
    const { slope, rSquared } = this.linearRegression(logScales, logCounts);
    const fractalDimension = -slope;

    // Confidence based on R-squared value
    const confidence = Math.max(0, Math.min(1, rSquared));

    return {
      confidence,
      parameters: {
        fractalDimension,
        rSquared,
        slope,
        scales: scales.length,
      },
    };
  }

  /**
   * Helper methods
   */
  private generateFibonacciSequence(count: number): number[] {
    const sequence = [1, 1];
    for (let i = 2; i < count; i++) {
      sequence.push(sequence[i - 1] + sequence[i - 2]);
    }
    return sequence;
  }

  private generateFibonacciLikeSequence(start: number, length: number): number[] {
    if (length <= 0) return [];
    if (length === 1) return [start];
    
    const sequence = [start, start];
    for (let i = 2; i < length; i++) {
      sequence.push(sequence[i - 1] + sequence[i - 2]);
    }
    return sequence;
  }

  private extractDominantFrequencies(magnitudes: number[], count: number = 10): number[] {
    const peaks = magnitudes
      .map((mag, index) => ({ magnitude: mag, frequency: index * this.config.sampleRate / (2 * magnitudes.length) }))
      .sort((a, b) => b.magnitude - a.magnitude)
      .slice(0, count)
      .map(peak => peak.frequency);
    
    return peaks.sort((a, b) => a - b);
  }

  private countBoxes(data: Float32Array, scale: number): number {
    // Simplified box counting for 1D data
    const boxSize = data.length / scale;
    let boxes = 0;
    
    for (let i = 0; i < scale; i++) {
      const start = Math.floor(i * boxSize);
      const end = Math.floor((i + 1) * boxSize);
      
      let hasData = false;
      for (let j = start; j < end && j < data.length; j++) {
        if (Math.abs(data[j]) > 0.01) {
          hasData = true;
          break;
        }
      }
      
      if (hasData) boxes++;
    }
    
    return boxes;
  }

  private linearRegression(x: number[], y: number[]): { slope: number; rSquared: number } {
    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate R-squared
    const yMean = sumY / n;
    const ssRes = y.reduce((sum, val, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(val - predicted, 2);
    }, 0);
    const ssTot = y.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0);
    const rSquared = 1 - (ssRes / ssTot);

    return { slope, rSquared };
  }

  private calculatePatternSimilarity(actual: number[], expected: number[]): PatternSimilarity {
    if (actual.length !== expected.length) {
      return {
        overallScore: 0,
        rhythmicSimilarity: 0,
        melodicSimilarity: 0,
        harmonicSimilarity: 0,
        spectralSimilarity: 0,
        temporalSimilarity: 0,
        analysisMethod: 'pattern_correlation',
        confidence: 0,
      };
    }

    // Calculate correlation coefficient
    const correlation = this.calculateCorrelation(actual, expected);
    const similarity = Math.max(0, correlation);

    return {
      overallScore: similarity,
      rhythmicSimilarity: similarity,
      melodicSimilarity: similarity * 0.8,
      harmonicSimilarity: similarity * 0.9,
      spectralSimilarity: similarity * 0.7,
      temporalSimilarity: similarity,
      analysisMethod: 'pattern_correlation',
      confidence: similarity,
    };
  }

  private calculateFrequencySimilarity(actual: number[], expected: number[]): PatternSimilarity {
    const correlation = this.calculateCorrelation(actual, expected);
    const similarity = Math.max(0, correlation);

    return {
      overallScore: similarity,
      rhythmicSimilarity: similarity * 0.6,
      melodicSimilarity: similarity * 0.9,
      harmonicSimilarity: similarity,
      spectralSimilarity: similarity,
      temporalSimilarity: similarity * 0.7,
      analysisMethod: 'frequency_correlation',
      confidence: similarity,
    };
  }

  private calculateCorrelation(x: number[], y: number[]): number {
    if (x.length !== y.length || x.length === 0) return 0;

    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator !== 0 ? numerator / denominator : 0;
  }
}

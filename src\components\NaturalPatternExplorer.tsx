'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  RotateCcw,
  Search,
  Filter,
  Info,
  Waves,
  Music,
  TreePine,
  Zap
} from 'lucide-react';

import { AudioEngine } from '@/lib/audio/AudioEngine';
import { PatternAnalyzer } from '@/lib/analysis/PatternAnalyzer';
import { 
  NATURAL_SOUNDS_DATABASE, 
  getNaturalSoundById,
  getNaturalSoundsByCategory,
  searchNaturalSounds 
} from '@/data/naturalSounds';
import { 
  NaturalSoundPattern, 
  NaturalSoundCategory,
  MusicalConnection,
  PatternRelative,
  MathematicalPatternType 
} from '@/types/NaturalPattern.types';

// Sub-components
import { SoundLibrary } from './explorer/SoundLibrary';
import { PatternVisualization } from './explorer/PatternVisualization';
import { MusicalConnections } from './explorer/MusicalConnections';
import { PatternRelatives } from './explorer/PatternRelatives';
import { AudioControls } from './explorer/AudioControls';
import { PatternAnalysisPanel } from './explorer/PatternAnalysisPanel';

interface NaturalPatternExplorerProps {
  className?: string;
  initialPatternId?: string;
  showAdvancedControls?: boolean;
  enableAnalysis?: boolean;
}

interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLooping: boolean;
  isMuted: boolean;
}

interface AnalysisState {
  isAnalyzing: boolean;
  results: any[];
  error: string | null;
  progress: number;
}

export const NaturalPatternExplorer: React.FC<NaturalPatternExplorerProps> = ({
  className = '',
  initialPatternId,
  showAdvancedControls = true,
  enableAnalysis = true,
}) => {
  // Core state
  const [selectedPattern, setSelectedPattern] = useState<NaturalSoundPattern | null>(null);
  const [audioEngine] = useState(() => new AudioEngine());
  const [patternAnalyzer] = useState(() => new PatternAnalyzer());
  const [isEngineInitialized, setIsEngineInitialized] = useState(false);

  // Playback state
  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 0.7,
    isLooping: false,
    isMuted: false,
  });

  // Analysis state
  const [analysisState, setAnalysisState] = useState<AnalysisState>({
    isAnalyzing: false,
    results: [],
    error: null,
    progress: 0,
  });

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<NaturalSoundCategory | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'connections' | 'relatives' | 'analysis'>('overview');

  // Initialize audio engine on first user interaction
  const initializeAudioEngine = useCallback(async () => {
    if (!isEngineInitialized) {
      try {
        await audioEngine.initialize();
        setIsEngineInitialized(true);
        console.log('Audio engine initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio engine:', error);
      }
    }
  }, [audioEngine, isEngineInitialized]);

  // Load initial pattern
  useEffect(() => {
    if (initialPatternId) {
      const pattern = getNaturalSoundById(initialPatternId);
      if (pattern) {
        setSelectedPattern(pattern);
      }
    } else if (NATURAL_SOUNDS_DATABASE.length > 0) {
      setSelectedPattern(NATURAL_SOUNDS_DATABASE[0]);
    }
  }, [initialPatternId]);

  // Filtered patterns based on search and category
  const filteredPatterns = useMemo(() => {
    let patterns = NATURAL_SOUNDS_DATABASE;

    // Filter by category
    if (selectedCategory !== 'all') {
      patterns = getNaturalSoundsByCategory(selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      patterns = searchNaturalSounds(searchQuery.trim());
    }

    return patterns;
  }, [searchQuery, selectedCategory]);

  // Play/pause audio
  const handlePlayPause = useCallback(async () => {
    if (!selectedPattern || !isEngineInitialized) {
      await initializeAudioEngine();
      return;
    }

    try {
      if (playbackState.isPlaying) {
        // Pause logic would go here
        setPlaybackState(prev => ({ ...prev, isPlaying: false }));
      } else {
        // Play the selected pattern
        const { source, state } = await audioEngine.playNaturalSound(selectedPattern, {
          loop: playbackState.isLooping,
          volume: playbackState.isMuted ? 0 : playbackState.volume,
        });

        setPlaybackState(prev => ({
          ...prev,
          isPlaying: true,
          duration: state.duration,
        }));

        // Update current time periodically
        const updateTime = () => {
          if (audioEngine.isReady) {
            setPlaybackState(prev => ({
              ...prev,
              currentTime: audioEngine.currentTime,
            }));
          }
        };

        const timeInterval = setInterval(updateTime, 100);

        // Cleanup when audio ends
        source.addEventListener('ended', () => {
          clearInterval(timeInterval);
          setPlaybackState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }));
        });
      }
    } catch (error) {
      console.error('Playback error:', error);
    }
  }, [selectedPattern, isEngineInitialized, playbackState, audioEngine, initializeAudioEngine]);

  // Analyze pattern
  const analyzePattern = useCallback(async () => {
    if (!selectedPattern || !enableAnalysis) return;

    setAnalysisState({
      isAnalyzing: true,
      results: [],
      error: null,
      progress: 0,
    });

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setAnalysisState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90),
        }));
      }, 200);

      const results = await patternAnalyzer.analyzePattern(selectedPattern);

      clearInterval(progressInterval);
      setAnalysisState({
        isAnalyzing: false,
        results,
        error: null,
        progress: 100,
      });
    } catch (error) {
      setAnalysisState({
        isAnalyzing: false,
        results: [],
        error: error instanceof Error ? error.message : 'Analysis failed',
        progress: 0,
      });
    }
  }, [selectedPattern, enableAnalysis, patternAnalyzer]);

  // Auto-analyze when pattern changes
  useEffect(() => {
    if (selectedPattern && enableAnalysis) {
      analyzePattern();
    }
  }, [selectedPattern, enableAnalysis, analyzePattern]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      audioEngine.dispose();
    };
  }, [audioEngine]);

  if (!selectedPattern) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <div className="text-center">
          <Waves className="w-16 h-16 mx-auto mb-4 text-cosmic-400 animate-pulse" />
          <h3 className="text-xl font-semibold text-white mb-2">Loading Natural Patterns...</h3>
          <p className="text-white/70">Preparing the cosmic sound library</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`natural-pattern-explorer ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gradient-cosmic mb-2">
              Natural Pattern Explorer
            </h1>
            <p className="text-white/80 text-lg">
              Discover the mathematical connections between nature's sounds and musical traditions
            </p>
          </div>
          
          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
              <input
                type="text"
                placeholder="Search patterns..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg 
                         text-white placeholder-white/50 focus:outline-none focus:ring-2 
                         focus:ring-cosmic-400 focus:border-transparent backdrop-blur-sm"
              />
            </div>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`btn-secondary ${showFilters ? 'bg-white/20' : ''}`}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="glass p-4 mb-4"
            >
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    selectedCategory === 'all' 
                      ? 'bg-cosmic-500 text-white' 
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  All Categories
                </button>
                {Object.values(NaturalSoundCategory).map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors capitalize ${
                      selectedCategory === category 
                        ? 'bg-cosmic-500 text-white' 
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    {category.replace('_', ' ')}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Panel - Sound Library */}
        <div className="lg:col-span-1">
          <SoundLibrary
            patterns={filteredPatterns}
            selectedPattern={selectedPattern}
            onPatternSelect={setSelectedPattern}
            searchQuery={searchQuery}
          />
        </div>

        {/* Right Panel - Pattern Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Audio Controls */}
          <AudioControls
            pattern={selectedPattern}
            playbackState={playbackState}
            onPlayPause={handlePlayPause}
            onVolumeChange={(volume) => setPlaybackState(prev => ({ ...prev, volume }))}
            onToggleLoop={() => setPlaybackState(prev => ({ ...prev, isLooping: !prev.isLooping }))}
            onToggleMute={() => setPlaybackState(prev => ({ ...prev, isMuted: !prev.isMuted }))}
            isEngineReady={isEngineInitialized}
          />

          {/* Pattern Visualization */}
          <PatternVisualization
            pattern={selectedPattern}
            analysisResults={analysisState.results}
            isAnalyzing={analysisState.isAnalyzing}
          />

          {/* Tabs */}
          <div className="glass">
            <div className="flex border-b border-white/10">
              {[
                { id: 'overview', label: 'Overview', icon: Info },
                { id: 'connections', label: 'Musical Connections', icon: Music },
                { id: 'relatives', label: 'Pattern Relatives', icon: TreePine },
                { id: 'analysis', label: 'Analysis', icon: Zap },
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as any)}
                  className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === id
                      ? 'text-cosmic-400 border-b-2 border-cosmic-400'
                      : 'text-white/70 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {label}
                </button>
              ))}
            </div>

            <div className="p-6">
              <AnimatePresence mode="wait">
                {activeTab === 'overview' && (
                  <motion.div
                    key="overview"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="space-y-4"
                  >
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {selectedPattern.name}
                      </h3>
                      <p className="text-white/80 mb-4">
                        {selectedPattern.description}
                      </p>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-white/60">Duration:</span>
                          <span className="text-white ml-2">{selectedPattern.duration}s</span>
                        </div>
                        <div>
                          <span className="text-white/60">Category:</span>
                          <span className="text-white ml-2 capitalize">
                            {selectedPattern.category.replace('_', ' ')}
                          </span>
                        </div>
                        <div>
                          <span className="text-white/60">Fundamental Freq:</span>
                          <span className="text-white ml-2">
                            {selectedPattern.metadata.fundamentalFrequency}Hz
                          </span>
                        </div>
                        <div>
                          <span className="text-white/60">Tempo:</span>
                          <span className="text-white ml-2">
                            {selectedPattern.metadata.tempo || 'N/A'} BPM
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mt-4">
                      {selectedPattern.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </motion.div>
                )}

                {activeTab === 'connections' && (
                  <motion.div
                    key="connections"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <MusicalConnections connections={selectedPattern.musicalConnections} />
                  </motion.div>
                )}

                {activeTab === 'relatives' && (
                  <motion.div
                    key="relatives"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <PatternRelatives relatives={selectedPattern.patternRelatives} />
                  </motion.div>
                )}

                {activeTab === 'analysis' && enableAnalysis && (
                  <motion.div
                    key="analysis"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <PatternAnalysisPanel
                      analysisState={analysisState}
                      onAnalyze={analyzePattern}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

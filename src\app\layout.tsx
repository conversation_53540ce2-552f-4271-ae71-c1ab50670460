import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Cosmic Music-Nature Pattern Discovery',
  description: 'Explore the mathematical connections between Indian Classical Music and natural phenomena through interactive pattern analysis and discovery.',
  keywords: [
    'Indian Classical Music',
    'Raga Analysis',
    'Natural Patterns',
    'Microtonal Music',
    'Pattern Recognition',
    'Music Theory',
    'Fractal Analysis',
    'Golden Ratio',
    'Fibonacci Sequences'
  ],
  authors: [{ name: 'Cosmic Music Discovery Team' }],
  creator: 'Cosmic Music Discovery Platform',
  publisher: 'Cosmic Music Discovery Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://cosmic-music-discovery.com'),
  openGraph: {
    title: 'Cosmic Music-Nature Pattern Discovery',
    description: 'Explore the mathematical connections between Indian Classical Music and natural phenomena',
    url: 'https://cosmic-music-discovery.com',
    siteName: 'Cosmic Music Discovery',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Cosmic Music-Nature Pattern Discovery Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Cosmic Music-Nature Pattern Discovery',
    description: 'Explore the mathematical connections between Indian Classical Music and natural phenomena',
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.className} antialiased bg-gradient-cosmic min-h-screen`}>
        <div className="relative min-h-screen">
          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-[url('/patterns/cosmic-bg.svg')] opacity-5 pointer-events-none" />
          
          {/* Main content */}
          <main className="relative z-10">
            {children}
          </main>
          
          {/* Performance monitoring script */}
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Core Web Vitals monitoring
                function sendToAnalytics(metric) {
                  // Implementation for performance monitoring
                  console.log('Performance metric:', metric);
                }
                
                // Monitor Largest Contentful Paint
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    sendToAnalytics({
                      name: 'LCP',
                      value: entry.startTime,
                      id: 'lcp'
                    });
                  }
                }).observe({entryTypes: ['largest-contentful-paint']});
                
                // Monitor First Input Delay
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    sendToAnalytics({
                      name: 'FID',
                      value: entry.processingStart - entry.startTime,
                      id: 'fid'
                    });
                  }
                }).observe({entryTypes: ['first-input']});
                
                // Monitor Cumulative Layout Shift
                let clsValue = 0;
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                      clsValue += entry.value;
                      sendToAnalytics({
                        name: 'CLS',
                        value: clsValue,
                        id: 'cls'
                      });
                    }
                  }
                }).observe({entryTypes: ['layout-shift']});
              `,
            }}
          />
        </div>
      </body>
    </html>
  );
}

/**
 * Type definitions for Natural Pattern Explorer feature
 * Cosmic Music-Nature Pattern Discovery Platform
 */

// Core natural sound pattern interface
export interface NaturalSoundPattern {
  id: string;
  name: string;
  category: NaturalSoundCategory;
  description: string;
  audioUrl: string;
  duration: number; // in seconds
  metadata: SoundMetadata;
  waveformData: Float32Array;
  frequencyData: FrequencyAnalysis;
  rhythmicPatterns: RhythmicPattern[];
  musicalConnections: MusicalConnection[];
  patternRelatives: PatternRelative[];
  tags: string[];
  recordingInfo: RecordingInfo;
}

// Categories for natural sounds
export enum NaturalSoundCategory {
  WATER = 'water',
  WIND = 'wind',
  BIRDS = 'birds',
  INSECTS = 'insects',
  WEATHER = 'weather',
  GEOLOGICAL = 'geological',
  CELESTIAL = 'celestial',
  BIOLOGICAL = 'biological',
}

// Metadata for each sound
export interface SoundMetadata {
  dominantFrequencies: number[]; // Hz
  fundamentalFrequency: number; // Hz
  harmonicSeries: number[];
  spectralCentroid: number;
  spectralRolloff: number;
  zeroCrossingRate: number;
  mfcc: number[]; // Mel-frequency cepstral coefficients
  tempo?: number; // BPM if rhythmic
  key?: string; // Musical key if applicable
  mode?: 'major' | 'minor' | 'modal';
}

// Frequency analysis data
export interface FrequencyAnalysis {
  fftData: Float32Array;
  frequencyBins: number[];
  magnitudes: number[];
  phases: number[];
  spectralPeaks: SpectralPeak[];
  harmonicContent: HarmonicAnalysis;
}

export interface SpectralPeak {
  frequency: number;
  magnitude: number;
  bandwidth: number;
  prominence: number;
}

export interface HarmonicAnalysis {
  fundamentalFreq: number;
  harmonics: Array<{
    harmonic: number;
    frequency: number;
    amplitude: number;
    phase: number;
  }>;
  harmonicity: number; // 0-1 scale
  inharmonicity: number;
}

// Rhythmic pattern analysis
export interface RhythmicPattern {
  id: string;
  name: string;
  pattern: number[]; // Time intervals in milliseconds
  tempo: number; // BPM
  timeSignature: string; // e.g., "4/4", "7/8"
  accentPattern: boolean[]; // Strong/weak beats
  complexity: number; // 0-1 scale
  similarity: PatternSimilarity[];
  mathematicalBasis: MathematicalPattern;
}

// Mathematical pattern relationships
export interface MathematicalPattern {
  type: MathematicalPatternType;
  parameters: Record<string, number>;
  confidence: number; // 0-1 scale
  description: string;
}

export enum MathematicalPatternType {
  FIBONACCI = 'fibonacci',
  GOLDEN_RATIO = 'golden_ratio',
  FRACTAL = 'fractal',
  HARMONIC_SERIES = 'harmonic_series',
  GEOMETRIC_PROGRESSION = 'geometric_progression',
  ARITHMETIC_PROGRESSION = 'arithmetic_progression',
  PRIME_SEQUENCE = 'prime_sequence',
  LOGARITHMIC_SPIRAL = 'logarithmic_spiral',
}

// Musical connections to artists and compositions
export interface MusicalConnection {
  id: string;
  type: MusicalConnectionType;
  title: string;
  artist: string;
  tradition: MusicalTradition;
  description: string;
  audioUrl?: string;
  videoUrl?: string;
  spotifyUrl?: string;
  youtubeUrl?: string;
  similarity: PatternSimilarity;
  culturalContext: CulturalContext;
  technicalAnalysis: TechnicalAnalysis;
}

export enum MusicalConnectionType {
  DIRECT_SAMPLING = 'direct_sampling',
  RHYTHMIC_INSPIRATION = 'rhythmic_inspiration',
  MELODIC_MIMICRY = 'melodic_mimicry',
  HARMONIC_REFLECTION = 'harmonic_reflection',
  TEXTURAL_SIMILARITY = 'textural_similarity',
  CONCEPTUAL_INSPIRATION = 'conceptual_inspiration',
}

export enum MusicalTradition {
  HINDUSTANI_CLASSICAL = 'hindustani_classical',
  CARNATIC_CLASSICAL = 'carnatic_classical',
  WESTERN_CLASSICAL = 'western_classical',
  JAZZ = 'jazz',
  FOLK = 'folk',
  CONTEMPORARY = 'contemporary',
  ELECTRONIC = 'electronic',
  WORLD_MUSIC = 'world_music',
}

// Cultural context information
export interface CulturalContext {
  region: string;
  timeAssociation?: TimeOfDay;
  seasonAssociation?: Season;
  ritualContext?: string;
  emotionalContext: EmotionalContext;
  historicalSignificance: string;
  modernRelevance: string;
}

export enum TimeOfDay {
  DAWN = 'dawn',
  MORNING = 'morning',
  MIDDAY = 'midday',
  AFTERNOON = 'afternoon',
  EVENING = 'evening',
  NIGHT = 'night',
  MIDNIGHT = 'midnight',
}

export enum Season {
  SPRING = 'spring',
  SUMMER = 'summer',
  MONSOON = 'monsoon',
  AUTUMN = 'autumn',
  WINTER = 'winter',
}

export interface EmotionalContext {
  primary: Emotion;
  secondary?: Emotion[];
  intensity: number; // 0-1 scale
  valence: number; // -1 to 1 (negative to positive)
  arousal: number; // 0-1 scale (calm to energetic)
}

export enum Emotion {
  JOY = 'joy',
  PEACE = 'peace',
  MELANCHOLY = 'melancholy',
  EXCITEMENT = 'excitement',
  CONTEMPLATION = 'contemplation',
  DEVOTION = 'devotion',
  LONGING = 'longing',
  CELEBRATION = 'celebration',
  MYSTERY = 'mystery',
  POWER = 'power',
}

// Technical analysis of musical connections
export interface TechnicalAnalysis {
  keySignature?: string;
  timeSignature: string;
  tempo: number;
  modalCharacteristics?: ModalCharacteristics;
  rhythmicComplexity: number; // 0-1 scale
  harmonicComplexity: number; // 0-1 scale
  melodicRange: number; // in semitones
  dynamicRange: number; // in dB
}

export interface ModalCharacteristics {
  mode: string;
  tonicNote: string;
  characteristicIntervals: number[];
  vadi?: string; // Principal note (Indian classical)
  samvadi?: string; // Secondary note (Indian classical)
}

// Pattern similarity scoring
export interface PatternSimilarity {
  overallScore: number; // 0-1 scale
  rhythmicSimilarity: number;
  melodicSimilarity: number;
  harmonicSimilarity: number;
  spectralSimilarity: number;
  temporalSimilarity: number;
  analysisMethod: string;
  confidence: number;
}

// Related patterns in nature and music
export interface PatternRelative {
  id: string;
  name: string;
  type: PatternRelativeType;
  description: string;
  similarity: PatternSimilarity;
  visualizationData?: VisualizationData;
  audioUrl?: string;
  category: string;
}

export enum PatternRelativeType {
  NATURAL_PHENOMENON = 'natural_phenomenon',
  MUSICAL_PATTERN = 'musical_pattern',
  MATHEMATICAL_SEQUENCE = 'mathematical_sequence',
  BIOLOGICAL_RHYTHM = 'biological_rhythm',
  ASTRONOMICAL_CYCLE = 'astronomical_cycle',
}

// Visualization data for patterns
export interface VisualizationData {
  type: VisualizationType;
  data: number[] | number[][];
  parameters: Record<string, any>;
  colorScheme: string[];
  animationDuration?: number;
}

export enum VisualizationType {
  WAVEFORM = 'waveform',
  SPECTRUM = 'spectrum',
  SPECTROGRAM = 'spectrogram',
  SPIRAL = 'spiral',
  FRACTAL = 'fractal',
  NETWORK = 'network',
  CIRCULAR = 'circular',
  LINEAR = 'linear',
}

// Recording information
export interface RecordingInfo {
  location: GeographicLocation;
  dateRecorded: string;
  timeRecorded: string;
  weather?: WeatherConditions;
  equipment: RecordingEquipment;
  recordedBy: string;
  license: string;
  source: string;
}

export interface GeographicLocation {
  latitude: number;
  longitude: number;
  altitude?: number;
  country: string;
  region: string;
  ecosystem: string;
  nearbyLandmarks?: string[];
}

export interface WeatherConditions {
  temperature: number; // Celsius
  humidity: number; // Percentage
  windSpeed: number; // km/h
  windDirection: string;
  pressure: number; // hPa
  visibility: number; // km
}

export interface RecordingEquipment {
  microphone: string;
  recorder: string;
  sampleRate: number; // Hz
  bitDepth: number;
  format: string;
  postProcessing?: string[];
}

// User interaction and preferences
export interface UserPreferences {
  favoriteCategories: NaturalSoundCategory[];
  preferredTraditions: MusicalTradition[];
  visualizationPreferences: VisualizationType[];
  audioQuality: 'low' | 'medium' | 'high';
  autoplay: boolean;
  showTechnicalDetails: boolean;
  culturalContextLevel: 'basic' | 'detailed' | 'expert';
}

// Search and filtering
export interface SearchFilters {
  categories: NaturalSoundCategory[];
  traditions: MusicalTradition[];
  emotions: Emotion[];
  timeAssociations: TimeOfDay[];
  mathematicalPatterns: MathematicalPatternType[];
  minSimilarity: number;
  maxDuration: number;
  hasAudioExamples: boolean;
}

// Analytics and performance tracking
export interface AnalyticsEvent {
  eventType: AnalyticsEventType;
  timestamp: number;
  patternId?: string;
  connectionId?: string;
  userAction: string;
  sessionId: string;
  metadata: Record<string, any>;
}

export enum AnalyticsEventType {
  PATTERN_VIEWED = 'pattern_viewed',
  AUDIO_PLAYED = 'audio_played',
  CONNECTION_EXPLORED = 'connection_explored',
  SIMILARITY_ANALYZED = 'similarity_analyzed',
  PATTERN_SHARED = 'pattern_shared',
  SEARCH_PERFORMED = 'search_performed',
  FILTER_APPLIED = 'filter_applied',
}

// Error handling
export interface PatternError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
  recoverable: boolean;
}

// API response types
export interface PatternExplorerResponse<T> {
  data: T;
  success: boolean;
  error?: PatternError;
  metadata: {
    timestamp: number;
    version: string;
    processingTime: number;
  };
}

{"name": "cosmic-music-nature-discovery", "version": "1.0.0", "description": "Cosmic Music-Nature Pattern Discovery Platform - Connecting Indian Classical Music with Natural Phenomena", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "zustand": "^4.4.7", "tone": "^14.7.77", "d3": "^7.8.5", "@types/d3": "^7.4.3", "ml-matrix": "^6.10.7", "fft-js": "^0.0.12"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["music-theory", "indian-classical-music", "pattern-recognition", "natural-phenomena", "microtonal-music", "raga-analysis", "fractal-patterns", "web-audio-api"], "author": "Cosmic Music Discovery Team", "license": "MIT"}
# Development Standards & Best Practices SOP
## Cosmic Music-Nature Pattern Discovery Platform

### Document Purpose
This Standard Operating Procedure (SOP) defines code quality standards, development practices, and technical guidelines for the Cosmic platform. All code must adhere to these standards to ensure maintainability, performance, and cultural authenticity.

---

## Code Quality Standards

### TypeScript/React/Next.js Standards

#### 1. TypeScript Configuration
```json
// tsconfig.json - Strict configuration required
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

#### 2. Component Structure Standards
```typescript
// Required component structure
interface ComponentProps {
  // All props must be explicitly typed
  requiredProp: string;
  optionalProp?: number;
  children?: React.ReactNode;
}

const ComponentName: React.FC<ComponentProps> = ({ 
  requiredProp, 
  optionalProp = defaultValue,
  children 
}) => {
  // Hooks at the top
  const [state, setState] = useState<StateType>(initialValue);
  const memoizedValue = useMemo(() => computation, [dependencies]);
  
  // Event handlers
  const handleEvent = useCallback((param: ParamType) => {
    // Implementation
  }, [dependencies]);
  
  // Early returns for loading/error states
  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent error={error} />;
  
  // Main render
  return (
    <div className="component-name">
      {children}
    </div>
  );
};

export default ComponentName;
```

#### 3. File Naming Conventions
- **Components**: PascalCase (`AudioEngine.tsx`, `RagaPlayer.tsx`)
- **Utilities**: camelCase (`audioUtils.ts`, `patternAnalysis.ts`)
- **Constants**: UPPER_SNAKE_CASE (`SHRUTI_RATIOS.ts`, `RAGA_DATABASE.ts`)
- **Types**: PascalCase with `.types.ts` suffix (`Audio.types.ts`)

#### 4. Import/Export Standards
```typescript
// Absolute imports preferred
import { AudioEngine } from '@/lib/audio/AudioEngine';
import { RagaData } from '@/types/Music.types';

// Named exports preferred over default exports for utilities
export const calculateShruti = (baseFreq: number, shrutiIndex: number): number => {
  // Implementation
};

// Default exports only for React components and main classes
export default class PatternAnalyzer {
  // Implementation
}
```

---

## Audio Programming Best Practices

### 1. Web Audio API Guidelines

#### Memory Management
```typescript
class AudioEngine {
  private audioContext: AudioContext;
  private activeNodes: Set<AudioNode> = new Set();
  
  constructor() {
    this.audioContext = new AudioContext();
  }
  
  // Always track and cleanup audio nodes
  createOscillator(): OscillatorNode {
    const oscillator = this.audioContext.createOscillator();
    this.activeNodes.add(oscillator);
    
    oscillator.addEventListener('ended', () => {
      this.activeNodes.delete(oscillator);
    });
    
    return oscillator;
  }
  
  // Mandatory cleanup method
  dispose(): void {
    this.activeNodes.forEach(node => {
      if ('stop' in node) {
        (node as OscillatorNode).stop();
      }
      node.disconnect();
    });
    this.activeNodes.clear();
    this.audioContext.close();
  }
}
```

#### Performance Optimization
```typescript
// Use Web Workers for heavy audio processing
// audioWorker.ts
self.onmessage = (event) => {
  const { audioData, analysisType } = event.data;
  
  switch (analysisType) {
    case 'fractal':
      const result = performFractalAnalysis(audioData);
      self.postMessage({ type: 'fractal-result', data: result });
      break;
    // Other analysis types
  }
};

// Main thread usage
const audioWorker = new Worker('/workers/audioWorker.js');
audioWorker.postMessage({ audioData, analysisType: 'fractal' });
```

#### Microtonal Precision Requirements
```typescript
// Frequency calculations must be accurate to ±0.1 cents
const CENT_RATIO = Math.pow(2, 1/1200);

class MicrotonalTuning {
  // Use high-precision arithmetic for shruti calculations
  calculateShrutiFrequency(baseFreq: number, shrutiIndex: number): number {
    const ratio = SHRUTI_RATIOS[shrutiIndex];
    const frequency = baseFreq * ratio;
    
    // Validate precision
    const cents = 1200 * Math.log2(frequency / baseFreq);
    const expectedCents = this.getExpectedCents(shrutiIndex);
    
    if (Math.abs(cents - expectedCents) > 0.1) {
      throw new Error(`Frequency calculation precision error: ${Math.abs(cents - expectedCents)} cents`);
    }
    
    return frequency;
  }
}
```

### 2. Real-time Audio Processing Standards

#### Latency Requirements
- **Target Latency**: <20ms for interactive audio
- **Buffer Size**: 256-512 samples for real-time processing
- **Sample Rate**: 44.1kHz minimum, 48kHz preferred

#### Performance Monitoring
```typescript
class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  
  measureAudioProcessing<T>(operation: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    this.metrics.get(operation)!.push(duration);
    
    // Alert if processing takes too long
    if (duration > 10) { // 10ms threshold
      console.warn(`Audio processing '${operation}' took ${duration}ms`);
    }
    
    return result;
  }
}
```

---

## Mathematical Algorithm Implementation Guidelines

### 1. Numerical Precision Standards
```typescript
// Use appropriate precision for different calculations
const EPSILON = 1e-10; // For floating-point comparisons
const FREQUENCY_PRECISION = 0.01; // Hz precision for audio
const RATIO_PRECISION = 1e-8; // For mathematical ratios

// Always validate numerical results
function validateFrequencyRatio(freq1: number, freq2: number, expectedRatio: number): boolean {
  const actualRatio = freq2 / freq1;
  return Math.abs(actualRatio - expectedRatio) < RATIO_PRECISION;
}
```

### 2. Pattern Recognition Algorithm Standards
```typescript
interface PatternAnalysisResult {
  confidence: number; // 0-1 scale
  patternType: PatternType;
  parameters: Record<string, number>;
  metadata: {
    processingTime: number;
    sampleSize: number;
    algorithm: string;
  };
}

abstract class PatternAnalyzer {
  abstract analyze(data: number[]): PatternAnalysisResult;
  
  // All analyzers must implement validation
  protected validateInput(data: number[]): void {
    if (!data || data.length === 0) {
      throw new Error('Input data cannot be empty');
    }
    
    if (data.some(value => !Number.isFinite(value))) {
      throw new Error('Input data contains invalid numbers');
    }
  }
  
  // Performance requirement: <100ms for typical analysis
  protected enforcePerformanceLimit(fn: () => PatternAnalysisResult): PatternAnalysisResult {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    if (duration > 100) {
      console.warn(`Pattern analysis exceeded performance limit: ${duration}ms`);
    }
    
    return result;
  }
}
```

### 3. Fractal Analysis Implementation
```typescript
class FractalAnalyzer extends PatternAnalyzer {
  analyze(data: number[]): PatternAnalysisResult {
    this.validateInput(data);
    
    return this.enforcePerformanceLimit(() => {
      const boxCountingDimension = this.calculateBoxCountingDimension(data);
      const selfSimilarity = this.analyzeSelfSimilarity(data);
      
      return {
        confidence: this.calculateConfidence(boxCountingDimension, selfSimilarity),
        patternType: PatternType.FRACTAL,
        parameters: {
          dimension: boxCountingDimension,
          selfSimilarity: selfSimilarity,
        },
        metadata: {
          processingTime: performance.now(),
          sampleSize: data.length,
          algorithm: 'box-counting',
        },
      };
    });
  }
  
  private calculateBoxCountingDimension(data: number[]): number {
    // Implementation must be mathematically accurate
    // Reference: Mandelbrot, B. B. (1982). The Fractal Geometry of Nature
    
    const scales = this.generateLogScales(data.length);
    const counts = scales.map(scale => this.countBoxes(data, scale));
    
    // Linear regression on log-log plot
    const logScales = scales.map(Math.log);
    const logCounts = counts.map(Math.log);
    
    const slope = this.linearRegression(logScales, logCounts).slope;
    return -slope; // Fractal dimension is negative slope
  }
}
```

---

## Testing Requirements

### 1. Unit Testing Standards
```typescript
// All functions must have unit tests with >90% coverage
describe('MicrotonalTuning', () => {
  let tuning: MicrotonalTuning;
  
  beforeEach(() => {
    tuning = new MicrotonalTuning();
  });
  
  describe('calculateShrutiFrequency', () => {
    it('should calculate accurate frequencies for all 22 shruti', () => {
      const baseFreq = 440; // A4
      
      SHRUTI_INDICES.forEach(index => {
        const frequency = tuning.calculateShrutiFrequency(baseFreq, index);
        const expectedRatio = SHRUTI_RATIOS[index];
        const actualRatio = frequency / baseFreq;
        
        expect(Math.abs(actualRatio - expectedRatio)).toBeLessThan(1e-8);
      });
    });
    
    it('should throw error for invalid shruti index', () => {
      expect(() => tuning.calculateShrutiFrequency(440, -1)).toThrow();
      expect(() => tuning.calculateShrutiFrequency(440, 23)).toThrow();
    });
  });
});
```

### 2. Integration Testing
```typescript
// Test complete workflows
describe('Raga Analysis Integration', () => {
  it('should identify raga from note sequence', async () => {
    const noteSequence = [
      { note: 'Sa', duration: 500 },
      { note: 'Re', duration: 300 },
      { note: 'Ga', duration: 400 },
      // ... complete sequence
    ];
    
    const analyzer = new RagaAnalyzer();
    const result = await analyzer.identifyRaga(noteSequence);
    
    expect(result.confidence).toBeGreaterThan(0.8);
    expect(result.ragaName).toBe('Yaman');
    expect(result.processingTime).toBeLessThan(1000);
  });
});
```

### 3. Performance Testing
```typescript
// Benchmark critical operations
describe('Performance Benchmarks', () => {
  it('should analyze patterns within performance limits', () => {
    const largeDataset = generateTestData(10000);
    const analyzer = new FractalAnalyzer();
    
    const start = performance.now();
    const result = analyzer.analyze(largeDataset);
    const duration = performance.now() - start;
    
    expect(duration).toBeLessThan(100); // 100ms limit
    expect(result.confidence).toBeGreaterThan(0);
  });
});
```

---

## Code Review Checklist

### Pre-Review Requirements
- [ ] All tests pass (unit, integration, performance)
- [ ] TypeScript compilation successful with no warnings
- [ ] ESLint and Prettier checks pass
- [ ] Performance benchmarks met
- [ ] Documentation updated

### Review Criteria

#### Code Quality (Required for Approval)
- [ ] Functions are pure where possible (no side effects)
- [ ] Error handling implemented for all failure modes
- [ ] Memory leaks prevented (audio nodes properly disposed)
- [ ] Performance optimizations applied where needed
- [ ] Code follows established patterns and conventions

#### Musical Theory Accuracy (Critical for Music-Related Code)
- [ ] Mathematical calculations verified against reference sources
- [ ] Cultural authenticity validated (for Indian classical music features)
- [ ] Frequency calculations accurate to specified precision
- [ ] Pattern recognition algorithms mathematically sound

#### Security Considerations
- [ ] No sensitive data logged to console
- [ ] Input validation implemented for all user inputs
- [ ] Audio data handling secure (no buffer overflows)
- [ ] External dependencies vetted for security issues

### Approval Process
1. **Self-Review**: Developer reviews own code against checklist
2. **Peer Review**: Senior developer reviews for technical accuracy
3. **Music Expert Review**: Required for raga/tala implementations
4. **Performance Review**: QA engineer validates performance benchmarks
5. **Final Approval**: Lead developer approves for merge

---

## Documentation Standards

### 1. Code Documentation
```typescript
/**
 * Calculates the frequency of a specific shruti in the 22-shruti system
 *
 * @param baseFreq - The base frequency (Sa) in Hz
 * @param shrutiIndex - Index of the shruti (0-21)
 * @returns The calculated frequency in Hz, accurate to ±0.1 cents
 *
 * @throws {Error} When shrutiIndex is out of range
 * @throws {Error} When frequency calculation precision is insufficient
 *
 * @example
 * ```typescript
 * const tuning = new MicrotonalTuning();
 * const frequency = tuning.calculateShrutiFrequency(440, 7); // Returns ~495.0Hz
 * ```
 *
 * @see {@link https://en.wikipedia.org/wiki/Shruti_(music)} for shruti theory
 */
calculateShrutiFrequency(baseFreq: number, shrutiIndex: number): number {
  // Implementation
}
```

### 2. Musical Theory Documentation
```typescript
/**
 * Raga: Yaman (Hindustani Classical)
 *
 * Aroha (Ascending): Ni Re Ga Ma# Dha Ni Sa'
 * Avaroha (Descending): Sa' Ni Dha Pa Ma# Ga Re Sa
 *
 * Vadi (Principal Note): Ga (Major Third)
 * Samvadi (Secondary Note): Ni (Major Seventh)
 *
 * Time: Evening (6 PM - 9 PM)
 * Mood: Devotional, Romantic
 *
 * Characteristic Phrases (Pakad):
 * - Ni Re Ga, Ma# Dha Ni
 * - Ga Ma# Dha, Pa Ma# Ga Re
 *
 * @reference Bhatkhande, V. N. (1934). Hindustani Sangeet Paddhati
 */
const RAGA_YAMAN: RagaDefinition = {
  // Implementation
};
```

### 3. Algorithm Documentation
```typescript
/**
 * Box-Counting Fractal Dimension Calculator
 *
 * Implements the box-counting method to estimate the fractal dimension
 * of a musical pattern or natural phenomenon data series.
 *
 * Algorithm:
 * 1. Generate logarithmic scale series
 * 2. Count boxes needed to cover data at each scale
 * 3. Perform linear regression on log-log plot
 * 4. Return negative slope as fractal dimension
 *
 * Time Complexity: O(n log n) where n is data length
 * Space Complexity: O(n)
 *
 * Accuracy: ±0.05 fractal dimension units
 * Performance: <100ms for datasets up to 10,000 points
 *
 * @reference Mandelbrot, B. B. (1982). The Fractal Geometry of Nature
 */
class BoxCountingAnalyzer {
  // Implementation
}
```

---

## Performance Optimization Guidelines

### 1. Audio Processing Optimization
```typescript
// Use object pooling for frequently created audio nodes
class AudioNodePool {
  private oscillatorPool: OscillatorNode[] = [];
  private gainPool: GainNode[] = [];

  getOscillator(): OscillatorNode {
    return this.oscillatorPool.pop() || this.audioContext.createOscillator();
  }

  returnOscillator(oscillator: OscillatorNode): void {
    oscillator.disconnect();
    this.oscillatorPool.push(oscillator);
  }
}

// Batch audio parameter changes
class AudioParameterScheduler {
  scheduleFrequencyChange(oscillator: OscillatorNode, frequency: number, time: number): void {
    // Use exponentialRampToValueAtTime for smooth transitions
    oscillator.frequency.exponentialRampToValueAtTime(frequency, time);
  }
}
```

### 2. Pattern Analysis Optimization
```typescript
// Use memoization for expensive calculations
const memoizedFractalAnalysis = useMemo(() => {
  return new Map<string, PatternAnalysisResult>();
}, []);

function analyzeFractalPattern(data: number[]): PatternAnalysisResult {
  const dataHash = hashArray(data);

  if (memoizedFractalAnalysis.has(dataHash)) {
    return memoizedFractalAnalysis.get(dataHash)!;
  }

  const result = performFractalAnalysis(data);
  memoizedFractalAnalysis.set(dataHash, result);

  return result;
}
```

### 3. React Performance Optimization
```typescript
// Use React.memo for expensive components
const PatternVisualization = React.memo<PatternVisualizationProps>(({
  patternData,
  visualizationType
}) => {
  // Expensive rendering logic
}, (prevProps, nextProps) => {
  // Custom comparison for complex props
  return deepEqual(prevProps.patternData, nextProps.patternData) &&
         prevProps.visualizationType === nextProps.visualizationType;
});

// Use useCallback for event handlers
const handlePatternAnalysis = useCallback((audioData: Float32Array) => {
  const worker = new Worker('/workers/patternAnalysis.js');
  worker.postMessage({ audioData });

  worker.onmessage = (event) => {
    setAnalysisResult(event.data);
  };
}, [setAnalysisResult]);
```

---

## Security Considerations

### 1. Audio Data Handling
```typescript
// Validate audio buffer sizes to prevent memory exhaustion
function validateAudioBuffer(buffer: AudioBuffer): void {
  const MAX_BUFFER_SIZE = 10 * 1024 * 1024; // 10MB limit
  const bufferSize = buffer.length * buffer.numberOfChannels * 4; // 4 bytes per float32

  if (bufferSize > MAX_BUFFER_SIZE) {
    throw new Error(`Audio buffer too large: ${bufferSize} bytes`);
  }
}

// Sanitize user-provided frequency values
function sanitizeFrequency(frequency: number): number {
  const MIN_FREQ = 20;    // 20 Hz
  const MAX_FREQ = 20000; // 20 kHz

  if (!Number.isFinite(frequency) || frequency < MIN_FREQ || frequency > MAX_FREQ) {
    throw new Error(`Invalid frequency: ${frequency}`);
  }

  return frequency;
}
```

### 2. Input Validation
```typescript
// Validate raga data input
function validateRagaInput(ragaData: Partial<RagaDefinition>): RagaDefinition {
  const schema = z.object({
    name: z.string().min(1).max(100),
    aroha: z.array(z.string()).min(5).max(12),
    avaroha: z.array(z.string()).min(5).max(12),
    vadi: z.string(),
    samvadi: z.string(),
    timeAssociation: z.enum(['morning', 'afternoon', 'evening', 'night']),
  });

  return schema.parse(ragaData);
}
```

This comprehensive SOP ensures consistent, high-quality code delivery while maintaining focus on audio performance, mathematical accuracy, and cultural authenticity.

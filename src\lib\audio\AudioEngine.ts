/**
 * Advanced Audio Engine for Cosmic Music-Nature Pattern Discovery Platform
 * Handles Web Audio API operations, microtonal synthesis, and pattern analysis
 */

import { NaturalSoundPattern, FrequencyAnalysis } from '@/types/NaturalPattern.types';

export interface AudioEngineConfig {
  sampleRate: number;
  bufferSize: number;
  maxPolyphony: number;
  enableAnalysis: boolean;
}

export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
}

export interface AnalysisResult {
  frequencyData: Float32Array;
  waveformData: Float32Array;
  spectralCentroid: number;
  spectralRolloff: number;
  zeroCrossingRate: number;
  rms: number;
  peak: number;
}

export class AudioEngine {
  private audioContext: AudioContext | null = null;
  private masterGain: GainNode | null = null;
  private analyser: AnalyserNode | null = null;
  private activeNodes: Set<AudioNode> = new Set();
  private audioBuffers: Map<string, AudioBuffer> = new Map();
  private config: AudioEngineConfig;
  private isInitialized = false;

  // Performance monitoring
  private performanceMetrics = {
    processingTime: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    latency: 0,
  };

  constructor(config: Partial<AudioEngineConfig> = {}) {
    this.config = {
      sampleRate: 48000,
      bufferSize: 512,
      maxPolyphony: 32,
      enableAnalysis: true,
      ...config,
    };
  }

  /**
   * Initialize the audio engine
   * Must be called after user interaction due to browser autoplay policies
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Create audio context with optimal settings
      this.audioContext = new AudioContext({
        sampleRate: this.config.sampleRate,
        latencyHint: 'interactive',
      });

      // Resume context if suspended (browser autoplay policy)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Create master gain node
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);
      this.masterGain.gain.value = 0.7; // Safe default volume

      // Create analyser for real-time analysis
      if (this.config.enableAnalysis) {
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048;
        this.analyser.smoothingTimeConstant = 0.8;
        this.analyser.connect(this.masterGain);
      }

      this.isInitialized = true;
      console.log('Audio Engine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Audio Engine:', error);
      throw new Error('Audio Engine initialization failed');
    }
  }

  /**
   * Load and decode audio file
   */
  async loadAudioFile(url: string, id: string): Promise<AudioBuffer> {
    if (!this.audioContext) {
      throw new Error('Audio Engine not initialized');
    }

    // Check if already loaded
    const cached = this.audioBuffers.get(id);
    if (cached) return cached;

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch audio: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      
      // Cache the buffer
      this.audioBuffers.set(id, audioBuffer);
      
      return audioBuffer;
    } catch (error) {
      console.error(`Failed to load audio file ${url}:`, error);
      throw error;
    }
  }

  /**
   * Play natural sound pattern with analysis
   */
  async playNaturalSound(
    pattern: NaturalSoundPattern,
    options: {
      loop?: boolean;
      volume?: number;
      startTime?: number;
      playbackRate?: number;
    } = {}
  ): Promise<{ source: AudioBufferSourceNode; state: PlaybackState }> {
    if (!this.audioContext || !this.masterGain) {
      throw new Error('Audio Engine not initialized');
    }

    const {
      loop = false,
      volume = 1.0,
      startTime = 0,
      playbackRate = 1.0,
    } = options;

    try {
      // Load audio buffer
      const audioBuffer = await this.loadAudioFile(pattern.audioUrl, pattern.id);

      // Create source node
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.loop = loop;
      source.playbackRate.value = playbackRate;

      // Create gain node for individual volume control
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = Math.max(0, Math.min(1, volume));

      // Connect audio graph
      source.connect(gainNode);
      gainNode.connect(this.analyser || this.masterGain);

      // Track active nodes for cleanup
      this.activeNodes.add(source);
      this.activeNodes.add(gainNode);

      // Setup cleanup on end
      source.addEventListener('ended', () => {
        this.activeNodes.delete(source);
        this.activeNodes.delete(gainNode);
        source.disconnect();
        gainNode.disconnect();
      });

      // Start playback
      const currentTime = this.audioContext.currentTime;
      source.start(currentTime, startTime);

      // Create playback state
      const state: PlaybackState = {
        isPlaying: true,
        currentTime: startTime,
        duration: audioBuffer.duration,
        volume,
        playbackRate,
      };

      return { source, state };
    } catch (error) {
      console.error('Failed to play natural sound:', error);
      throw error;
    }
  }

  /**
   * Generate microtonal tone based on 22-shruti system
   */
  createMicrotonalTone(
    baseFrequency: number,
    shrutiIndex: number,
    duration: number = 1.0,
    waveform: OscillatorType = 'sine'
  ): { oscillator: OscillatorNode; gain: GainNode } {
    if (!this.audioContext || !this.masterGain) {
      throw new Error('Audio Engine not initialized');
    }

    // 22-shruti frequency ratios (simplified version)
    const shrutiRatios = [
      1.0000, 1.0667, 1.1111, 1.1852, 1.2500, 1.3333, 1.4074,
      1.5000, 1.6000, 1.6667, 1.7778, 1.8519, 2.0000, 2.1333,
      2.2222, 2.3704, 2.5000, 2.6667, 2.8148, 3.0000, 3.2000, 3.3333
    ];

    const frequency = baseFrequency * (shrutiRatios[shrutiIndex] || 1.0);

    // Create oscillator
    const oscillator = this.audioContext.createOscillator();
    oscillator.type = waveform;
    oscillator.frequency.value = frequency;

    // Create ADSR envelope
    const gainNode = this.audioContext.createGain();
    const attackTime = 0.1;
    const decayTime = 0.2;
    const sustainLevel = 0.7;
    const releaseTime = 0.3;

    const currentTime = this.audioContext.currentTime;
    
    // ADSR envelope
    gainNode.gain.setValueAtTime(0, currentTime);
    gainNode.gain.linearRampToValueAtTime(1, currentTime + attackTime);
    gainNode.gain.linearRampToValueAtTime(sustainLevel, currentTime + attackTime + decayTime);
    gainNode.gain.setValueAtTime(sustainLevel, currentTime + duration - releaseTime);
    gainNode.gain.linearRampToValueAtTime(0, currentTime + duration);

    // Connect nodes
    oscillator.connect(gainNode);
    gainNode.connect(this.masterGain);

    // Track for cleanup
    this.activeNodes.add(oscillator);
    this.activeNodes.add(gainNode);

    // Auto-cleanup
    oscillator.addEventListener('ended', () => {
      this.activeNodes.delete(oscillator);
      this.activeNodes.delete(gainNode);
      oscillator.disconnect();
      gainNode.disconnect();
    });

    // Start and stop
    oscillator.start(currentTime);
    oscillator.stop(currentTime + duration);

    return { oscillator, gain: gainNode };
  }

  /**
   * Real-time audio analysis
   */
  getAnalysisData(): AnalysisResult | null {
    if (!this.analyser) return null;

    const bufferLength = this.analyser.frequencyBinCount;
    const frequencyData = new Float32Array(bufferLength);
    const waveformData = new Float32Array(bufferLength);

    this.analyser.getFloatFrequencyData(frequencyData);
    this.analyser.getFloatTimeDomainData(waveformData);

    // Calculate additional metrics
    const spectralCentroid = this.calculateSpectralCentroid(frequencyData);
    const spectralRolloff = this.calculateSpectralRolloff(frequencyData);
    const zeroCrossingRate = this.calculateZeroCrossingRate(waveformData);
    const rms = this.calculateRMS(waveformData);
    const peak = Math.max(...waveformData.map(Math.abs));

    return {
      frequencyData,
      waveformData,
      spectralCentroid,
      spectralRolloff,
      zeroCrossingRate,
      rms,
      peak,
    };
  }

  /**
   * Calculate spectral centroid (brightness measure)
   */
  private calculateSpectralCentroid(frequencyData: Float32Array): number {
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < frequencyData.length; i++) {
      const magnitude = Math.pow(10, frequencyData[i] / 20); // Convert dB to linear
      const frequency = (i * this.config.sampleRate) / (2 * frequencyData.length);
      
      numerator += frequency * magnitude;
      denominator += magnitude;
    }

    return denominator > 0 ? numerator / denominator : 0;
  }

  /**
   * Calculate spectral rolloff (frequency below which 85% of energy is contained)
   */
  private calculateSpectralRolloff(frequencyData: Float32Array): number {
    const magnitudes = frequencyData.map(db => Math.pow(10, db / 20));
    const totalEnergy = magnitudes.reduce((sum, mag) => sum + mag * mag, 0);
    const threshold = 0.85 * totalEnergy;

    let cumulativeEnergy = 0;
    for (let i = 0; i < magnitudes.length; i++) {
      cumulativeEnergy += magnitudes[i] * magnitudes[i];
      if (cumulativeEnergy >= threshold) {
        return (i * this.config.sampleRate) / (2 * frequencyData.length);
      }
    }

    return this.config.sampleRate / 2; // Nyquist frequency
  }

  /**
   * Calculate zero crossing rate (measure of noisiness)
   */
  private calculateZeroCrossingRate(waveformData: Float32Array): number {
    let crossings = 0;
    for (let i = 1; i < waveformData.length; i++) {
      if ((waveformData[i] >= 0) !== (waveformData[i - 1] >= 0)) {
        crossings++;
      }
    }
    return crossings / (waveformData.length - 1);
  }

  /**
   * Calculate RMS (Root Mean Square) energy
   */
  private calculateRMS(waveformData: Float32Array): number {
    const sumSquares = waveformData.reduce((sum, sample) => sum + sample * sample, 0);
    return Math.sqrt(sumSquares / waveformData.length);
  }

  /**
   * Set master volume
   */
  setMasterVolume(volume: number): void {
    if (this.masterGain) {
      const clampedVolume = Math.max(0, Math.min(1, volume));
      this.masterGain.gain.setValueAtTime(clampedVolume, this.audioContext!.currentTime);
    }
  }

  /**
   * Get current performance metrics
   */
  getPerformanceMetrics() {
    return { ...this.performanceMetrics };
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    // Stop all active nodes
    this.activeNodes.forEach(node => {
      if ('stop' in node) {
        try {
          (node as AudioScheduledSourceNode).stop();
        } catch (e) {
          // Node might already be stopped
        }
      }
      node.disconnect();
    });

    this.activeNodes.clear();
    this.audioBuffers.clear();

    // Close audio context
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.masterGain = null;
    this.analyser = null;
    this.isInitialized = false;

    console.log('Audio Engine disposed');
  }

  /**
   * Check if engine is ready for use
   */
  get isReady(): boolean {
    return this.isInitialized && this.audioContext?.state === 'running';
  }

  /**
   * Get current audio context time
   */
  get currentTime(): number {
    return this.audioContext?.currentTime || 0;
  }
}

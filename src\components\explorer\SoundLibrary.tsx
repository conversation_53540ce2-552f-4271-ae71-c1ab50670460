'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Volume2, 
  Clock, 
  MapPin, 
  Calendar,
  Waves,
  Wind,
  Droplets,
  Bird,
  Zap,
  TreePine,
  Sun,
  Cloud
} from 'lucide-react';

import { 
  NaturalSoundPattern, 
  NaturalSoundCategory 
} from '@/types/NaturalPattern.types';

interface SoundLibraryProps {
  patterns: NaturalSoundPattern[];
  selectedPattern: NaturalSoundPattern | null;
  onPatternSelect: (pattern: NaturalSoundPattern) => void;
  searchQuery: string;
}

interface SoundCardProps {
  pattern: NaturalSoundPattern;
  isSelected: boolean;
  onSelect: () => void;
  isPlaying?: boolean;
}

// Category icons mapping
const categoryIcons = {
  [NaturalSoundCategory.WATER]: Droplets,
  [NaturalSoundCategory.WIND]: Wind,
  [NaturalSoundCategory.BIRDS]: <PERSON>,
  [NaturalSoundCategory.WEATHER]: Cloud,
  [NaturalSoundCategory.FOREST]: TreePine,
  [NaturalSoundCategory.GEOLOGICAL]: Zap,
  [NaturalSoundCategory.ATMOSPHERIC]: Sun,
};

// Category colors
const categoryColors = {
  [NaturalSoundCategory.WATER]: 'from-blue-500/20 to-cyan-500/20 border-blue-400/30',
  [NaturalSoundCategory.WIND]: 'from-gray-500/20 to-slate-500/20 border-gray-400/30',
  [NaturalSoundCategory.BIRDS]: 'from-yellow-500/20 to-orange-500/20 border-yellow-400/30',
  [NaturalSoundCategory.WEATHER]: 'from-purple-500/20 to-indigo-500/20 border-purple-400/30',
  [NaturalSoundCategory.FOREST]: 'from-green-500/20 to-emerald-500/20 border-green-400/30',
  [NaturalSoundCategory.GEOLOGICAL]: 'from-red-500/20 to-pink-500/20 border-red-400/30',
  [NaturalSoundCategory.ATMOSPHERIC]: 'from-amber-500/20 to-yellow-500/20 border-amber-400/30',
};

const SoundCard: React.FC<SoundCardProps> = ({ 
  pattern, 
  isSelected, 
  onSelect, 
  isPlaying = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const CategoryIcon = categoryIcons[pattern.category] || Waves;
  const categoryColorClass = categoryColors[pattern.category] || categoryColors[NaturalSoundCategory.WATER];

  // Format duration
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Format location
  const formatLocation = (location: any): string => {
    if (location.region && location.country) {
      return `${location.region}, ${location.country}`;
    }
    return location.country || 'Unknown';
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`
        relative cursor-pointer rounded-xl border backdrop-blur-sm transition-all duration-300
        ${isSelected 
          ? `bg-gradient-to-br ${categoryColorClass} ring-2 ring-cosmic-400 shadow-lg shadow-cosmic-400/20` 
          : `bg-gradient-to-br ${categoryColorClass} hover:shadow-lg hover:shadow-white/10`
        }
      `}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Playing indicator */}
      {isPlaying && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-2 -right-2 w-6 h-6 bg-cosmic-500 rounded-full flex items-center justify-center z-10"
        >
          <Volume2 className="w-3 h-3 text-white" />
        </motion.div>
      )}

      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className={`
              p-2 rounded-lg transition-colors duration-300
              ${isSelected ? 'bg-white/20' : 'bg-white/10'}
            `}>
              <CategoryIcon className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-white text-sm truncate">
                {pattern.name}
              </h3>
              <p className="text-white/60 text-xs capitalize">
                {pattern.category.replace('_', ' ')}
              </p>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-white/80 text-xs mb-3 line-clamp-2">
          {pattern.description}
        </p>

        {/* Metadata */}
        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-1 text-white/60">
              <Clock className="w-3 h-3" />
              <span>{formatDuration(pattern.duration)}</span>
            </div>
            <div className="flex items-center space-x-1 text-white/60">
              <Waves className="w-3 h-3" />
              <span>{pattern.metadata.fundamentalFrequency}Hz</span>
            </div>
          </div>

          {pattern.recordingInfo && (
            <div className="flex items-center space-x-1 text-white/60 text-xs">
              <MapPin className="w-3 h-3 flex-shrink-0" />
              <span className="truncate">
                {formatLocation(pattern.recordingInfo.location)}
              </span>
            </div>
          )}

          {pattern.recordingInfo?.dateRecorded && (
            <div className="flex items-center space-x-1 text-white/60 text-xs">
              <Calendar className="w-3 h-3" />
              <span>{new Date(pattern.recordingInfo.dateRecorded).toLocaleDateString()}</span>
            </div>
          )}
        </div>

        {/* Mathematical patterns indicator */}
        {pattern.rhythmicPatterns.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {pattern.rhythmicPatterns.slice(0, 2).map((rhythmicPattern, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full"
                >
                  {rhythmicPattern.mathematicalBasis?.type.replace('_', ' ') || 'Pattern'}
                </span>
              ))}
              {pattern.rhythmicPatterns.length > 2 && (
                <span className="px-2 py-1 bg-white/10 text-white/60 text-xs rounded-full">
                  +{pattern.rhythmicPatterns.length - 2}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {pattern.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-white/10 text-white/70 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
          {pattern.tags.length > 3 && (
            <span className="px-2 py-1 bg-white/10 text-white/60 text-xs rounded-full">
              +{pattern.tags.length - 3}
            </span>
          )}
        </div>

        {/* Hover overlay */}
        <AnimatePresence>
          {isHovered && !isSelected && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-white/5 rounded-xl flex items-center justify-center"
            >
              <div className="flex items-center space-x-2 text-white">
                <Play className="w-4 h-4" />
                <span className="text-sm font-medium">Play Pattern</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export const SoundLibrary: React.FC<SoundLibraryProps> = ({
  patterns,
  selectedPattern,
  onPatternSelect,
  searchQuery,
}) => {
  const [sortBy, setSortBy] = useState<'name' | 'duration' | 'category' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Sort patterns
  const sortedPatterns = useMemo(() => {
    const sorted = [...patterns].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'duration':
          comparison = a.duration - b.duration;
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
        case 'date':
          const dateA = a.recordingInfo?.dateRecorded || '1970-01-01';
          const dateB = b.recordingInfo?.dateRecorded || '1970-01-01';
          comparison = new Date(dateA).getTime() - new Date(dateB).getTime();
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [patterns, sortBy, sortOrder]);

  // Group patterns by category for better organization
  const groupedPatterns = useMemo(() => {
    const groups: Record<string, NaturalSoundPattern[]> = {};
    
    sortedPatterns.forEach(pattern => {
      const category = pattern.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(pattern);
    });

    return groups;
  }, [sortedPatterns]);

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  if (patterns.length === 0) {
    return (
      <div className="glass p-6 text-center">
        <Waves className="w-12 h-12 mx-auto mb-4 text-white/40" />
        <h3 className="text-lg font-semibold text-white mb-2">No Patterns Found</h3>
        <p className="text-white/60">
          {searchQuery 
            ? `No patterns match "${searchQuery}"`
            : 'No patterns available in this category'
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white">
          Sound Library
          <span className="text-white/60 text-sm ml-2">
            ({patterns.length} pattern{patterns.length !== 1 ? 's' : ''})
          </span>
        </h2>

        {/* Sort controls */}
        <div className="flex items-center space-x-2">
          <select
            value={sortBy}
            onChange={(e) => handleSort(e.target.value as typeof sortBy)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-sm text-white 
                     focus:outline-none focus:ring-2 focus:ring-cosmic-400 focus:border-transparent"
          >
            <option value="name">Name</option>
            <option value="duration">Duration</option>
            <option value="category">Category</option>
            <option value="date">Date</option>
          </select>
          
          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="p-1 text-white/60 hover:text-white transition-colors"
            title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      {/* Pattern Grid */}
      <div className="space-y-6 max-h-[70vh] overflow-y-auto custom-scrollbar">
        {Object.entries(groupedPatterns).map(([category, categoryPatterns]) => (
          <div key={category}>
            {/* Category Header */}
            <div className="flex items-center space-x-2 mb-3">
              {React.createElement(categoryIcons[category as NaturalSoundCategory] || Waves, {
                className: "w-4 h-4 text-white/60"
              })}
              <h3 className="text-sm font-medium text-white/80 capitalize">
                {category.replace('_', ' ')} ({categoryPatterns.length})
              </h3>
            </div>

            {/* Pattern Cards */}
            <div className="grid gap-3">
              <AnimatePresence>
                {categoryPatterns.map((pattern) => (
                  <SoundCard
                    key={pattern.id}
                    pattern={pattern}
                    isSelected={selectedPattern?.id === pattern.id}
                    onSelect={() => onPatternSelect(pattern)}
                    isPlaying={false} // This would be connected to actual playback state
                  />
                ))}
              </AnimatePresence>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

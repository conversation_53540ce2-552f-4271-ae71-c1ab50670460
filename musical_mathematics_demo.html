<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematical Music Theory</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .frequency-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .freq-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .freq-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .chord-section {
            margin: 20px 0;
        }
        
        .progression-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .math-formula {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 18px;
            text-align: center;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .golden-ratio {
            background: linear-gradient(45deg, #f39c12, #f1c40f);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .scale-visualizer {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .note {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .note.white {
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .note.black {
            background: #34495e;
            color: white;
        }
        
        .note:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 Mathematical Music Theory 🎵</h1>
        
        <div class="section">
            <h2>Frequency Ratios & Harmonic Series</h2>
            <div class="math-formula">
                Octave: 2:1 | Perfect Fifth: 3:2 | Perfect Fourth: 4:3 | Major Third: 5:4
            </div>
            
            <div class="frequency-demo">
                <div class="freq-card">
                    <h3>Octave (2:1)</h3>
                    <p>440Hz → 880Hz</p>
                    <button onclick="playInterval(440, 880, 2000)">Play Octave</button>
                </div>
                <div class="freq-card">
                    <h3>Perfect Fifth (3:2)</h3>
                    <p>440Hz → 660Hz</p>
                    <button onclick="playInterval(440, 660, 2000)">Play Fifth</button>
                </div>
                <div class="freq-card">
                    <h3>Major Third (5:4)</h3>
                    <p>440Hz → 550Hz</p>
                    <button onclick="playInterval(440, 550, 2000)">Play Third</button>
                </div>
                <div class="freq-card">
                    <h3>Tritone (√2:1)</h3>
                    <p>440Hz → 622Hz</p>
                    <button onclick="playInterval(440, 622, 2000)">Play Tritone</button>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Major Scale Mathematics</h2>
            <div class="math-formula">
                Pattern: W-W-H-W-W-W-H (W=Whole step, H=Half step)
            </div>
            
            <div class="scale-visualizer">
                <div class="note white" onclick="playNote(261.63)">C</div>
                <div class="note white" onclick="playNote(293.66)">D</div>
                <div class="note white" onclick="playNote(329.63)">E</div>
                <div class="note white" onclick="playNote(349.23)">F</div>
                <div class="note white" onclick="playNote(392.00)">G</div>
                <div class="note white" onclick="playNote(440.00)">A</div>
                <div class="note white" onclick="playNote(493.88)">B</div>
                <div class="note white" onclick="playNote(523.25)">C</div>
            </div>
            
            <button onclick="playScale()">Play C Major Scale</button>
        </div>
        
        <div class="section">
            <h2>Chord Mathematics</h2>
            <div class="chord-section">
                <h3>Major Triad (C-E-G)</h3>
                <div class="math-formula">
                    Root (1:1) + Major 3rd (5:4) + Perfect 5th (3:2)
                </div>
                <button onclick="playChord([261.63, 329.63, 392.00])">Play C Major</button>
                
                <h3>Minor Triad (A-C-E)</h3>
                <div class="math-formula">
                    Root (1:1) + Minor 3rd (6:5) + Perfect 5th (3:2)
                </div>
                <button onclick="playChord([220.00, 261.63, 329.63])">Play A Minor</button>
            </div>
        </div>
        
        <div class="golden-ratio">
            <h2>🌟 Golden Ratio in Music</h2>
            <p>φ ≈ 1.618 - The divine proportion found in nature and beautiful music</p>
            <p>Place your musical climax at ~62% through your composition for maximum impact!</p>
        </div>
        
        <div class="section">
            <h2>Common Chord Progression: I-V-vi-IV</h2>
            <div class="math-formula">
                C Major - G Major - A minor - F Major
            </div>
            
            <div class="progression-demo">
                <button onclick="playProgression()">Play Full Progression</button>
                <button onclick="playChord([261.63, 329.63, 392.00])">I (C)</button>
                <button onclick="playChord([392.00, 493.88, 587.33])">V (G)</button>
                <button onclick="playChord([220.00, 261.63, 329.63])">vi (Am)</button>
                <button onclick="playChord([174.61, 220.00, 261.63])">IV (F)</button>
            </div>
        </div>
        
        <div class="section">
            <h2>Fibonacci in Music Structure</h2>
            <div class="math-formula">
                Sequence: 1, 1, 2, 3, 5, 8, 13, 21...
            </div>
            <p>Use these numbers for:</p>
            <ul>
                <li>Phrase lengths (2, 3, 5, 8 measures)</li>
                <li>Section divisions</li>
                <li>Rhythmic patterns</li>
                <li>Harmonic change timing</li>
            </ul>
            <button onclick="playFibonacciRhythm()">Play Fibonacci Rhythm</button>
        </div>
    </div>

    <script>
        let audioContext;
        let currentOscillators = [];
        
        function initAudio() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
        }
        
        function stopAllOscillators() {
            currentOscillators.forEach(osc => {
                try {
                    osc.stop();
                } catch (e) {}
            });
            currentOscillators = [];
        }
        
        function playNote(frequency, duration = 500) {
            initAudio();
            stopAllOscillators();
            
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
            
            currentOscillators.push(oscillator);
        }
        
        function playInterval(freq1, freq2, duration = 2000) {
            initAudio();
            stopAllOscillators();
            
            // Play first note
            setTimeout(() => playNote(freq1, 800), 0);
            // Play second note
            setTimeout(() => playNote(freq2, 800), 400);
            // Play both together
            setTimeout(() => {
                playChord([freq1, freq2], 1000);
            }, 1000);
        }
        
        function playChord(frequencies, duration = 1000) {
            initAudio();
            stopAllOscillators();
            
            frequencies.forEach(freq => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration / 1000);
                
                currentOscillators.push(oscillator);
            });
        }
        
        function playScale() {
            const cMajorFreqs = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88, 523.25];
            
            cMajorFreqs.forEach((freq, index) => {
                setTimeout(() => playNote(freq, 400), index * 300);
            });
        }
        
        function playProgression() {
            const chords = [
                [261.63, 329.63, 392.00], // C Major
                [392.00, 493.88, 587.33], // G Major
                [220.00, 261.63, 329.63], // A minor
                [174.61, 220.00, 261.63]  // F Major
            ];
            
            chords.forEach((chord, index) => {
                setTimeout(() => playChord(chord, 1200), index * 1000);
            });
        }
        
        function playFibonacciRhythm() {
            // Play rhythm based on Fibonacci sequence: 1,1,2,3,5
            const rhythm = [200, 200, 400, 600, 1000]; // milliseconds
            let totalTime = 0;
            
            rhythm.forEach((duration, index) => {
                setTimeout(() => {
                    playNote(440, duration * 0.8);
                }, totalTime);
                totalTime += duration;
            });
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            stopAllOscillators();
            if (audioContext) {
                audioContext.close();
            }
        });
    </script>
</body>
</html>
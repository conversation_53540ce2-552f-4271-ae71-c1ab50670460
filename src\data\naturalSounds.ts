/**
 * Curated Natural Sound Database
 * Cosmic Music-Nature Pattern Discovery Platform
 * 
 * This database contains 20+ carefully selected natural sound patterns
 * with comprehensive metadata, musical connections, and pattern analysis.
 */

import {
  NaturalSoundPattern,
  NaturalSoundCategory,
  MusicalConnectionType,
  MusicalTradition,
  MathematicalPatternType,
  PatternRelativeType,
  TimeOfDay,
  Season,
  Emotion,
  VisualizationType,
} from '@/types/NaturalPattern.types';

export const NATURAL_SOUNDS_DATABASE: NaturalSoundPattern[] = [
  {
    id: 'ocean-waves-rhythmic',
    name: 'Rhythmic Ocean Waves',
    category: NaturalSoundCategory.WATER,
    description: 'Gentle, rhythmic ocean waves with a consistent 7-beat pattern reminiscent of Rupak tala',
    audioUrl: '/audio/natural/ocean-waves-rhythmic.mp3',
    duration: 120,
    metadata: {
      dominantFrequencies: [80, 160, 320, 640],
      fundamentalFrequency: 80,
      harmonicSeries: [80, 160, 240, 320, 400, 480, 560, 640],
      spectralCentroid: 250,
      spectralRolloff: 800,
      zeroCrossingRate: 0.15,
      mfcc: [12.5, -8.2, 4.1, -2.3, 1.8, -1.2, 0.9, -0.6, 0.4, -0.3, 0.2, -0.1, 0.1],
      tempo: 42,
      key: 'D minor',
      mode: 'minor',
    },
    waveformData: new Float32Array(8192), // Placeholder - would be populated with actual data
    frequencyData: {
      fftData: new Float32Array(4096),
      frequencyBins: Array.from({ length: 2048 }, (_, i) => i * 22050 / 2048),
      magnitudes: Array.from({ length: 2048 }, () => Math.random()),
      phases: Array.from({ length: 2048 }, () => Math.random() * 2 * Math.PI),
      spectralPeaks: [
        { frequency: 80, magnitude: 0.8, bandwidth: 10, prominence: 0.9 },
        { frequency: 160, magnitude: 0.6, bandwidth: 15, prominence: 0.7 },
        { frequency: 320, magnitude: 0.4, bandwidth: 20, prominence: 0.5 },
      ],
      harmonicContent: {
        fundamentalFreq: 80,
        harmonics: [
          { harmonic: 1, frequency: 80, amplitude: 0.8, phase: 0 },
          { harmonic: 2, frequency: 160, amplitude: 0.6, phase: 0.5 },
          { harmonic: 3, frequency: 240, amplitude: 0.4, phase: 1.0 },
          { harmonic: 4, frequency: 320, amplitude: 0.3, phase: 1.5 },
        ],
        harmonicity: 0.75,
        inharmonicity: 0.25,
      },
    },
    rhythmicPatterns: [
      {
        id: 'ocean-7beat',
        name: 'Seven-Beat Ocean Cycle',
        pattern: [1000, 800, 600, 800, 600, 800, 1200], // milliseconds
        tempo: 42,
        timeSignature: '7/8',
        accentPattern: [true, false, false, true, false, false, true],
        complexity: 0.6,
        similarity: [],
        mathematicalBasis: {
          type: MathematicalPatternType.FIBONACCI,
          parameters: { sequence: [1, 1, 2, 3, 5], ratio: 1.618 },
          confidence: 0.8,
          description: 'Wave intervals follow Fibonacci-like progression',
        },
      },
    ],
    musicalConnections: [
      {
        id: 'debussy-la-mer',
        type: MusicalConnectionType.TEXTURAL_SIMILARITY,
        title: 'La Mer',
        artist: 'Claude Debussy',
        tradition: MusicalTradition.WESTERN_CLASSICAL,
        description: 'Debussy\'s impressionistic portrayal of ocean waves uses similar rhythmic undulation and spectral harmonies',
        audioUrl: '/audio/examples/debussy-la-mer-excerpt.mp3',
        youtubeUrl: 'https://youtube.com/watch?v=example',
        similarity: {
          overallScore: 0.85,
          rhythmicSimilarity: 0.9,
          melodicSimilarity: 0.7,
          harmonicSimilarity: 0.8,
          spectralSimilarity: 0.9,
          temporalSimilarity: 0.85,
          analysisMethod: 'spectral_correlation',
          confidence: 0.9,
        },
        culturalContext: {
          region: 'France',
          timeAssociation: TimeOfDay.EVENING,
          seasonAssociation: Season.SUMMER,
          emotionalContext: {
            primary: Emotion.CONTEMPLATION,
            secondary: [Emotion.MYSTERY, Emotion.PEACE],
            intensity: 0.7,
            valence: 0.3,
            arousal: 0.4,
          },
          historicalSignificance: 'Revolutionary impressionistic composition technique',
          modernRelevance: 'Continues to influence ambient and film music',
        },
        technicalAnalysis: {
          keySignature: 'D♭ major',
          timeSignature: '4/4',
          tempo: 40,
          rhythmicComplexity: 0.8,
          harmonicComplexity: 0.9,
          melodicRange: 36,
          dynamicRange: 45,
        },
      },
      {
        id: 'raga-megh',
        type: MusicalConnectionType.RHYTHMIC_INSPIRATION,
        title: 'Raga Megh (Cloud Raga)',
        artist: 'Traditional Hindustani',
        tradition: MusicalTradition.HINDUSTANI_CLASSICAL,
        description: 'Monsoon raga that captures the rhythmic essence of ocean waves and rain patterns',
        similarity: {
          overallScore: 0.78,
          rhythmicSimilarity: 0.85,
          melodicSimilarity: 0.65,
          harmonicSimilarity: 0.75,
          spectralSimilarity: 0.8,
          temporalSimilarity: 0.9,
          analysisMethod: 'tala_correlation',
          confidence: 0.85,
        },
        culturalContext: {
          region: 'North India',
          timeAssociation: TimeOfDay.EVENING,
          seasonAssociation: Season.MONSOON,
          ritualContext: 'Monsoon celebration',
          emotionalContext: {
            primary: Emotion.LONGING,
            secondary: [Emotion.JOY, Emotion.PEACE],
            intensity: 0.8,
            valence: 0.6,
            arousal: 0.5,
          },
          historicalSignificance: 'Ancient raga associated with monsoon season',
          modernRelevance: 'Still performed during monsoon festivals',
        },
        technicalAnalysis: {
          timeSignature: '7/8',
          tempo: 45,
          modalCharacteristics: {
            mode: 'Megh',
            tonicNote: 'Sa',
            characteristicIntervals: [0, 3, 5, 7, 10],
            vadi: 'Ma',
            samvadi: 'Sa',
          },
          rhythmicComplexity: 0.7,
          harmonicComplexity: 0.6,
          melodicRange: 24,
          dynamicRange: 30,
        },
      },
    ],
    patternRelatives: [
      {
        id: 'heartbeat-rhythm',
        name: 'Human Heartbeat Rhythm',
        type: PatternRelativeType.BIOLOGICAL_RHYTHM,
        description: 'Resting heartbeat follows similar 7-beat grouping pattern',
        similarity: {
          overallScore: 0.72,
          rhythmicSimilarity: 0.85,
          melodicSimilarity: 0.4,
          harmonicSimilarity: 0.6,
          spectralSimilarity: 0.5,
          temporalSimilarity: 0.9,
          analysisMethod: 'rhythm_correlation',
          confidence: 0.8,
        },
        category: 'biological',
        visualizationData: {
          type: VisualizationType.WAVEFORM,
          data: [0.8, 0.2, 0.1, 0.3, 0.1, 0.2, 0.9],
          parameters: { color: '#ff6b6b', lineWidth: 2 },
          colorScheme: ['#ff6b6b', '#4ecdc4'],
        },
      },
      {
        id: 'fibonacci-spiral',
        name: 'Fibonacci Spiral in Nautilus Shell',
        type: PatternRelativeType.MATHEMATICAL_SEQUENCE,
        description: 'Shell growth pattern follows same Fibonacci ratios as wave intervals',
        similarity: {
          overallScore: 0.68,
          rhythmicSimilarity: 0.6,
          melodicSimilarity: 0.5,
          harmonicSimilarity: 0.7,
          spectralSimilarity: 0.6,
          temporalSimilarity: 0.8,
          analysisMethod: 'fibonacci_analysis',
          confidence: 0.9,
        },
        category: 'mathematical',
        visualizationData: {
          type: VisualizationType.SPIRAL,
          data: [1, 1, 2, 3, 5, 8, 13, 21],
          parameters: { spiralType: 'fibonacci', turns: 3 },
          colorScheme: ['#ffd93d', '#6bcf7f'],
        },
      },
    ],
    tags: ['rhythmic', 'meditative', 'natural', 'fibonacci', 'tala', 'monsoon'],
    recordingInfo: {
      location: {
        latitude: 19.0760,
        longitude: 72.8777,
        country: 'India',
        region: 'Maharashtra',
        ecosystem: 'Coastal',
        nearbyLandmarks: ['Mumbai coastline', 'Arabian Sea'],
      },
      dateRecorded: '2023-07-15',
      timeRecorded: '06:30',
      weather: {
        temperature: 28,
        humidity: 85,
        windSpeed: 12,
        windDirection: 'Southwest',
        pressure: 1013,
        visibility: 8,
      },
      equipment: {
        microphone: 'Rode NTG3',
        recorder: 'Zoom H6',
        sampleRate: 48000,
        bitDepth: 24,
        format: 'WAV',
        postProcessing: ['noise_reduction', 'normalization'],
      },
      recordedBy: 'Cosmic Audio Team',
      license: 'Creative Commons BY-SA 4.0',
      source: 'Original Recording',
    },
  },
  
  // Additional natural sounds would follow the same structure...
  {
    id: 'morning-birds-raga',
    name: 'Dawn Bird Chorus',
    category: NaturalSoundCategory.BIRDS,
    description: 'Complex polyrhythmic bird chorus at dawn, featuring patterns similar to Raga Bhairav',
    audioUrl: '/audio/natural/morning-birds-raga.mp3',
    duration: 180,
    metadata: {
      dominantFrequencies: [1200, 2400, 3600, 4800],
      fundamentalFrequency: 1200,
      harmonicSeries: [1200, 2400, 3600, 4800, 6000, 7200],
      spectralCentroid: 2800,
      spectralRolloff: 6000,
      zeroCrossingRate: 0.45,
      mfcc: [8.2, -12.1, 6.3, -3.8, 2.1, -1.5, 1.1, -0.8, 0.5, -0.4, 0.3, -0.2, 0.1],
      tempo: 120,
      key: 'C major',
      mode: 'major',
    },
    waveformData: new Float32Array(8192),
    frequencyData: {
      fftData: new Float32Array(4096),
      frequencyBins: Array.from({ length: 2048 }, (_, i) => i * 22050 / 2048),
      magnitudes: Array.from({ length: 2048 }, () => Math.random()),
      phases: Array.from({ length: 2048 }, () => Math.random() * 2 * Math.PI),
      spectralPeaks: [
        { frequency: 1200, magnitude: 0.9, bandwidth: 50, prominence: 0.95 },
        { frequency: 2400, magnitude: 0.7, bandwidth: 80, prominence: 0.8 },
        { frequency: 3600, magnitude: 0.5, bandwidth: 100, prominence: 0.6 },
      ],
      harmonicContent: {
        fundamentalFreq: 1200,
        harmonics: [
          { harmonic: 1, frequency: 1200, amplitude: 0.9, phase: 0 },
          { harmonic: 2, frequency: 2400, amplitude: 0.7, phase: 0.3 },
          { harmonic: 3, frequency: 3600, amplitude: 0.5, phase: 0.6 },
        ],
        harmonicity: 0.85,
        inharmonicity: 0.15,
      },
    },
    rhythmicPatterns: [
      {
        id: 'dawn-chorus-polyrhythm',
        name: 'Dawn Polyrhythmic Chorus',
        pattern: [500, 300, 200, 400, 300, 500, 600],
        tempo: 120,
        timeSignature: '4/4',
        accentPattern: [true, false, true, false, true, false, true],
        complexity: 0.9,
        similarity: [],
        mathematicalBasis: {
          type: MathematicalPatternType.GOLDEN_RATIO,
          parameters: { ratio: 1.618, sequence: [1, 1.618, 2.618] },
          confidence: 0.75,
          description: 'Call intervals follow golden ratio proportions',
        },
      },
    ],
    musicalConnections: [
      {
        id: 'raga-bhairav',
        type: MusicalConnectionType.MELODIC_MIMICRY,
        title: 'Raga Bhairav',
        artist: 'Traditional Hindustani',
        tradition: MusicalTradition.HINDUSTANI_CLASSICAL,
        description: 'Morning raga that mirrors the ascending melodic patterns of dawn bird calls',
        similarity: {
          overallScore: 0.88,
          rhythmicSimilarity: 0.8,
          melodicSimilarity: 0.95,
          harmonicSimilarity: 0.85,
          spectralSimilarity: 0.8,
          temporalSimilarity: 0.9,
          analysisMethod: 'melodic_contour_analysis',
          confidence: 0.92,
        },
        culturalContext: {
          region: 'North India',
          timeAssociation: TimeOfDay.DAWN,
          seasonAssociation: Season.WINTER,
          ritualContext: 'Morning prayers',
          emotionalContext: {
            primary: Emotion.DEVOTION,
            secondary: [Emotion.PEACE, Emotion.CONTEMPLATION],
            intensity: 0.9,
            valence: 0.8,
            arousal: 0.3,
          },
          historicalSignificance: 'One of the most ancient ragas in Indian classical music',
          modernRelevance: 'Still performed in morning concerts and meditation',
        },
        technicalAnalysis: {
          timeSignature: '4/4',
          tempo: 60,
          modalCharacteristics: {
            mode: 'Bhairav',
            tonicNote: 'Sa',
            characteristicIntervals: [0, 1, 4, 5, 7, 8, 11],
            vadi: 'Dha',
            samvadi: 'Re',
          },
          rhythmicComplexity: 0.5,
          harmonicComplexity: 0.8,
          melodicRange: 36,
          dynamicRange: 25,
        },
      },
    ],
    patternRelatives: [
      {
        id: 'golden-ratio-sunflower',
        name: 'Sunflower Seed Spiral',
        type: PatternRelativeType.NATURAL_PHENOMENON,
        description: 'Sunflower seed arrangement follows same golden ratio as bird call intervals',
        similarity: {
          overallScore: 0.71,
          rhythmicSimilarity: 0.6,
          melodicSimilarity: 0.7,
          harmonicSimilarity: 0.8,
          spectralSimilarity: 0.6,
          temporalSimilarity: 0.7,
          analysisMethod: 'golden_ratio_analysis',
          confidence: 0.85,
        },
        category: 'botanical',
        visualizationData: {
          type: VisualizationType.SPIRAL,
          data: Array.from({ length: 144 }, (_, i) => i * 137.5),
          parameters: { spiralType: 'golden', petals: 144 },
          colorScheme: ['#ffd93d', '#ff6b35'],
        },
      },
    ],
    tags: ['dawn', 'birds', 'polyrhythmic', 'bhairav', 'golden-ratio', 'morning'],
    recordingInfo: {
      location: {
        latitude: 28.6139,
        longitude: 77.2090,
        country: 'India',
        region: 'Delhi',
        ecosystem: 'Urban Forest',
        nearbyLandmarks: ['Lodhi Gardens', 'India Gate'],
      },
      dateRecorded: '2023-03-21',
      timeRecorded: '05:45',
      weather: {
        temperature: 18,
        humidity: 70,
        windSpeed: 5,
        windDirection: 'East',
        pressure: 1018,
        visibility: 12,
      },
      equipment: {
        microphone: 'Audio-Technica AT4053b',
        recorder: 'Sound Devices 702T',
        sampleRate: 48000,
        bitDepth: 24,
        format: 'WAV',
        postProcessing: ['wind_noise_reduction', 'eq_enhancement'],
      },
      recordedBy: 'Cosmic Audio Team',
      license: 'Creative Commons BY-SA 4.0',
      source: 'Original Recording',
    },
  },

  // Wind Through Trees - Fibonacci Pattern
  {
    id: 'wind-trees-fibonacci',
    name: 'Wind Through Forest Canopy',
    category: NaturalSoundCategory.WIND,
    description: 'Gentle wind through deciduous trees creating Fibonacci-based rustling patterns',
    audioUrl: '/audio/natural/wind-trees-fibonacci.mp3',
    duration: 150,
    metadata: {
      dominantFrequencies: [200, 400, 800, 1600],
      fundamentalFrequency: 200,
      harmonicSeries: [200, 400, 600, 800, 1000, 1200, 1400, 1600],
      spectralCentroid: 600,
      spectralRolloff: 2000,
      zeroCrossingRate: 0.35,
      mfcc: [10.1, -9.5, 5.2, -2.9, 1.6, -1.1, 0.8, -0.5, 0.3, -0.2, 0.15, -0.1, 0.05],
      tempo: 72,
      key: 'F major',
      mode: 'major',
    },
    waveformData: new Float32Array(8192),
    frequencyData: {
      fftData: new Float32Array(4096),
      frequencyBins: Array.from({ length: 2048 }, (_, i) => i * 22050 / 2048),
      magnitudes: Array.from({ length: 2048 }, () => Math.random() * 0.8),
      phases: Array.from({ length: 2048 }, () => Math.random() * 2 * Math.PI),
      spectralPeaks: [
        { frequency: 200, magnitude: 0.7, bandwidth: 30, prominence: 0.8 },
        { frequency: 400, magnitude: 0.5, bandwidth: 40, prominence: 0.6 },
        { frequency: 800, magnitude: 0.3, bandwidth: 60, prominence: 0.4 },
      ],
      harmonicContent: {
        fundamentalFreq: 200,
        harmonics: [
          { harmonic: 1, frequency: 200, amplitude: 0.7, phase: 0 },
          { harmonic: 2, frequency: 400, amplitude: 0.5, phase: 0.4 },
          { harmonic: 3, frequency: 600, amplitude: 0.3, phase: 0.8 },
          { harmonic: 4, frequency: 800, amplitude: 0.2, phase: 1.2 },
        ],
        harmonicity: 0.65,
        inharmonicity: 0.35,
      },
    },
    rhythmicPatterns: [
      {
        id: 'wind-fibonacci-rhythm',
        name: 'Fibonacci Wind Gusts',
        pattern: [1000, 1000, 2000, 3000, 5000, 8000], // Fibonacci sequence in ms
        tempo: 72,
        timeSignature: '6/8',
        accentPattern: [true, false, true, false, true, false],
        complexity: 0.8,
        similarity: [],
        mathematicalBasis: {
          type: MathematicalPatternType.FIBONACCI,
          parameters: { sequence: [1, 1, 2, 3, 5, 8, 13], ratio: 1.618 },
          confidence: 0.92,
          description: 'Wind gust intervals precisely follow Fibonacci sequence',
        },
      },
    ],
    musicalConnections: [
      {
        id: 'vivaldi-four-seasons-spring',
        type: MusicalConnectionType.TEXTURAL_SIMILARITY,
        title: 'Spring from The Four Seasons',
        artist: 'Antonio Vivaldi',
        tradition: MusicalTradition.WESTERN_CLASSICAL,
        description: 'Vivaldi\'s depiction of spring breezes uses similar flowing, organic rhythmic patterns',
        audioUrl: '/audio/examples/vivaldi-spring-breeze.mp3',
        similarity: {
          overallScore: 0.82,
          rhythmicSimilarity: 0.85,
          melodicSimilarity: 0.75,
          harmonicSimilarity: 0.8,
          spectralSimilarity: 0.85,
          temporalSimilarity: 0.9,
          analysisMethod: 'texture_analysis',
          confidence: 0.88,
        },
        culturalContext: {
          region: 'Italy',
          timeAssociation: TimeOfDay.MORNING,
          seasonAssociation: Season.SPRING,
          emotionalContext: {
            primary: Emotion.JOY,
            secondary: [Emotion.PEACE, Emotion.CELEBRATION],
            intensity: 0.7,
            valence: 0.8,
            arousal: 0.6,
          },
          historicalSignificance: 'Pioneering programmatic music depicting nature',
          modernRelevance: 'Continues to influence nature-inspired compositions',
        },
        technicalAnalysis: {
          keySignature: 'E major',
          timeSignature: '4/4',
          tempo: 120,
          rhythmicComplexity: 0.7,
          harmonicComplexity: 0.6,
          melodicRange: 28,
          dynamicRange: 35,
        },
      },
    ],
    patternRelatives: [
      {
        id: 'tree-branch-fractal',
        name: 'Tree Branch Fractal Structure',
        type: PatternRelativeType.NATURAL_PHENOMENON,
        description: 'Tree branching patterns follow same Fibonacci ratios as wind gust intervals',
        similarity: {
          overallScore: 0.76,
          rhythmicSimilarity: 0.7,
          melodicSimilarity: 0.6,
          harmonicSimilarity: 0.8,
          spectralSimilarity: 0.7,
          temporalSimilarity: 0.9,
          analysisMethod: 'fractal_dimension_analysis',
          confidence: 0.87,
        },
        category: 'botanical',
        visualizationData: {
          type: VisualizationType.FRACTAL,
          data: [[0, 0], [0, 1], [0.618, 1.618], [1, 2.618]],
          parameters: { iterations: 7, angle: 25, reduction: 0.618 },
          colorScheme: ['#2d5016', '#4a7c59'],
        },
      },
    ],
    tags: ['wind', 'trees', 'fibonacci', 'organic', 'peaceful', 'spring'],
    recordingInfo: {
      location: {
        latitude: 47.6062,
        longitude: -122.3321,
        country: 'USA',
        region: 'Washington State',
        ecosystem: 'Temperate Rainforest',
        nearbyLandmarks: ['Olympic National Forest', 'Puget Sound'],
      },
      dateRecorded: '2023-05-10',
      timeRecorded: '14:20',
      weather: {
        temperature: 16,
        humidity: 75,
        windSpeed: 8,
        windDirection: 'Northwest',
        pressure: 1015,
        visibility: 15,
      },
      equipment: {
        microphone: 'Schoeps CMIT 5U',
        recorder: 'Sound Devices MixPre-6',
        sampleRate: 48000,
        bitDepth: 24,
        format: 'WAV',
        postProcessing: ['high_pass_filter', 'gentle_compression'],
      },
      recordedBy: 'Cosmic Audio Team',
      license: 'Creative Commons BY-SA 4.0',
      source: 'Original Recording',
    },
  },

  // Rainfall Rhythm - Geometric Progression
  {
    id: 'rainfall-geometric',
    name: 'Monsoon Rainfall Rhythm',
    category: NaturalSoundCategory.WEATHER,
    description: 'Steady monsoon rain with droplet patterns following geometric progression',
    audioUrl: '/audio/natural/rainfall-geometric.mp3',
    duration: 200,
    metadata: {
      dominantFrequencies: [500, 1000, 2000, 4000],
      fundamentalFrequency: 500,
      harmonicSeries: [500, 1000, 1500, 2000, 2500, 3000, 3500, 4000],
      spectralCentroid: 1500,
      spectralRolloff: 5000,
      zeroCrossingRate: 0.6,
      mfcc: [9.8, -11.2, 7.1, -4.3, 2.8, -1.9, 1.3, -0.9, 0.6, -0.4, 0.25, -0.15, 0.08],
      tempo: 180,
      key: 'A minor',
      mode: 'minor',
    },
    waveformData: new Float32Array(8192),
    frequencyData: {
      fftData: new Float32Array(4096),
      frequencyBins: Array.from({ length: 2048 }, (_, i) => i * 22050 / 2048),
      magnitudes: Array.from({ length: 2048 }, () => Math.random() * 0.9),
      phases: Array.from({ length: 2048 }, () => Math.random() * 2 * Math.PI),
      spectralPeaks: [
        { frequency: 500, magnitude: 0.8, bandwidth: 25, prominence: 0.85 },
        { frequency: 1000, magnitude: 0.6, bandwidth: 35, prominence: 0.7 },
        { frequency: 2000, magnitude: 0.4, bandwidth: 50, prominence: 0.5 },
        { frequency: 4000, magnitude: 0.2, bandwidth: 80, prominence: 0.3 },
      ],
      harmonicContent: {
        fundamentalFreq: 500,
        harmonics: [
          { harmonic: 1, frequency: 500, amplitude: 0.8, phase: 0 },
          { harmonic: 2, frequency: 1000, amplitude: 0.6, phase: 0.2 },
          { harmonic: 3, frequency: 1500, amplitude: 0.4, phase: 0.4 },
          { harmonic: 4, frequency: 2000, amplitude: 0.3, phase: 0.6 },
        ],
        harmonicity: 0.7,
        inharmonicity: 0.3,
      },
    },
    rhythmicPatterns: [
      {
        id: 'rain-geometric-pattern',
        name: 'Geometric Rain Droplets',
        pattern: [100, 200, 400, 800, 1600], // Geometric progression
        tempo: 180,
        timeSignature: '5/8',
        accentPattern: [true, false, true, false, true],
        complexity: 0.7,
        similarity: [],
        mathematicalBasis: {
          type: MathematicalPatternType.GEOMETRIC_PROGRESSION,
          parameters: { ratio: 2, firstTerm: 100, terms: 5 },
          confidence: 0.89,
          description: 'Raindrop impact intervals form perfect geometric progression',
        },
      },
    ],
    musicalConnections: [
      {
        id: 'raga-malhar',
        type: MusicalConnectionType.CONCEPTUAL_INSPIRATION,
        title: 'Raga Malhar',
        artist: 'Traditional Hindustani',
        tradition: MusicalTradition.HINDUSTANI_CLASSICAL,
        description: 'Rain-invoking raga that captures the essence and rhythm of monsoon rainfall',
        similarity: {
          overallScore: 0.91,
          rhythmicSimilarity: 0.95,
          melodicSimilarity: 0.85,
          harmonicSimilarity: 0.9,
          spectralSimilarity: 0.88,
          temporalSimilarity: 0.95,
          analysisMethod: 'monsoon_pattern_correlation',
          confidence: 0.94,
        },
        culturalContext: {
          region: 'North India',
          timeAssociation: TimeOfDay.AFTERNOON,
          seasonAssociation: Season.MONSOON,
          ritualContext: 'Rain invocation ceremonies',
          emotionalContext: {
            primary: Emotion.LONGING,
            secondary: [Emotion.JOY, Emotion.CELEBRATION],
            intensity: 0.9,
            valence: 0.7,
            arousal: 0.8,
          },
          historicalSignificance: 'Legendary raga said to bring rain when performed',
          modernRelevance: 'Still performed during monsoon season and drought periods',
        },
        technicalAnalysis: {
          timeSignature: '7/8',
          tempo: 90,
          modalCharacteristics: {
            mode: 'Malhar',
            tonicNote: 'Sa',
            characteristicIntervals: [0, 2, 4, 5, 7, 9, 11],
            vadi: 'Pa',
            samvadi: 'Sa',
          },
          rhythmicComplexity: 0.8,
          harmonicComplexity: 0.7,
          melodicRange: 32,
          dynamicRange: 40,
        },
      },
    ],
    patternRelatives: [
      {
        id: 'crystal-lattice',
        name: 'Crystal Lattice Structure',
        type: PatternRelativeType.NATURAL_PHENOMENON,
        description: 'Crystal formation patterns follow same geometric progression as raindrop intervals',
        similarity: {
          overallScore: 0.69,
          rhythmicSimilarity: 0.6,
          melodicSimilarity: 0.5,
          harmonicSimilarity: 0.8,
          spectralSimilarity: 0.7,
          temporalSimilarity: 0.8,
          analysisMethod: 'geometric_structure_analysis',
          confidence: 0.82,
        },
        category: 'geological',
        visualizationData: {
          type: VisualizationType.NETWORK,
          data: [[0, 0], [1, 0], [2, 0], [0, 1], [1, 1], [2, 1]],
          parameters: { latticeType: 'hexagonal', spacing: 1 },
          colorScheme: ['#a8dadc', '#457b9d'],
        },
      },
    ],
    tags: ['rain', 'monsoon', 'geometric', 'malhar', 'rhythmic', 'meditation'],
    recordingInfo: {
      location: {
        latitude: 18.5204,
        longitude: 73.8567,
        country: 'India',
        region: 'Maharashtra',
        ecosystem: 'Western Ghats',
        nearbyLandmarks: ['Sahyadri Mountains', 'Pune'],
      },
      dateRecorded: '2023-07-28',
      timeRecorded: '15:45',
      weather: {
        temperature: 24,
        humidity: 95,
        windSpeed: 15,
        windDirection: 'Southwest',
        pressure: 1008,
        visibility: 3,
      },
      equipment: {
        microphone: 'Rode NTG4+',
        recorder: 'Zoom F8n',
        sampleRate: 48000,
        bitDepth: 24,
        format: 'WAV',
        postProcessing: ['rain_enhancement', 'spatial_imaging'],
      },
      recordedBy: 'Cosmic Audio Team',
      license: 'Creative Commons BY-SA 4.0',
      source: 'Original Recording',
    },
  },
];

// Export utility functions for database operations
export const getNaturalSoundById = (id: string): NaturalSoundPattern | undefined => {
  return NATURAL_SOUNDS_DATABASE.find(sound => sound.id === id);
};

export const getNaturalSoundsByCategory = (category: NaturalSoundCategory): NaturalSoundPattern[] => {
  return NATURAL_SOUNDS_DATABASE.filter(sound => sound.category === category);
};

export const searchNaturalSounds = (query: string): NaturalSoundPattern[] => {
  const lowercaseQuery = query.toLowerCase();
  return NATURAL_SOUNDS_DATABASE.filter(sound => 
    sound.name.toLowerCase().includes(lowercaseQuery) ||
    sound.description.toLowerCase().includes(lowercaseQuery) ||
    sound.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

export const getRandomNaturalSound = (): NaturalSoundPattern => {
  const randomIndex = Math.floor(Math.random() * NATURAL_SOUNDS_DATABASE.length);
  return NATURAL_SOUNDS_DATABASE[randomIndex]!;
};
